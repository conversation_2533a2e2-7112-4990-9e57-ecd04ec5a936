import React, { useState, useMemo } from 'react';
import { Input, Empty, Tag } from 'antd';
import { Search } from 'lucide-react';
import { categorySections } from '../data/metricDefinitions';
import MetricDefinitionCard from './MetricDefinitionCard';
import { MetricDefinition } from '../types/dictionaryTypes';

const SearchableMetrics: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const allMetrics = useMemo(() => {
    return categorySections.flatMap(section => section.metrics);
  }, []);

  const filteredMetrics = useMemo(() => {
    if (!searchTerm.trim()) return [];

    const term = searchTerm.toLowerCase();
    return allMetrics.filter(metric => 
      metric.name.toLowerCase().includes(term) ||
      (metric.calculation && metric.calculation.toLowerCase().includes(term))
    );
  }, [searchTerm, allMetrics]);

  const getCategoryForMetric = (metric: MetricDefinition): string => {
    const section = categorySections.find(section => 
      section.metrics.some(m => m.name === metric.name)
    );
    return section?.title || '';
  };

  return (
    <div className="mb-8">
      <div className="mb-6">
        <Input
          size="large"
          placeholder="Search for metrics by name or calculation..."
          prefix={<Search className="h-5 w-5 text-gray-400" />}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-2xl"
        />
      </div>

      {searchTerm.trim() && (
        <div>
          {filteredMetrics.length > 0 ? (
            <>
              <p className="text-gray-600 mb-4">
                Found {filteredMetrics.length} metric{filteredMetrics.length !== 1 ? 's' : ''} matching "{searchTerm}"
              </p>
              <div className="space-y-4">
                {filteredMetrics.map((metric, index) => (
                  <div key={index}>
                    <Tag color="purple" className="mb-2">
                      {getCategoryForMetric(metric)}
                    </Tag>
                    <MetricDefinitionCard metric={metric} />
                  </div>
                ))}
              </div>
            </>
          ) : (
            <Empty
              description={`No metrics found matching "${searchTerm}"`}
              className="py-8"
            />
          )}
        </div>
      )}
    </div>
  );
};

export default SearchableMetrics;