import React, { useState } from 'react';
import { Card, Tag } from 'antd';
import { ChevronDown, ChevronUp, Calculator, Target, Info } from 'lucide-react';
import { MetricDefinition, ThresholdLevel } from '../types/dictionaryTypes';

interface MetricDefinitionCardProps {
  metric: MetricDefinition;
  isExpanded?: boolean;
}

const getThresholdColor = (level: ThresholdLevel): string => {
  switch (level) {
    case 'green':
      return 'success';
    case 'yellow':
      return 'warning';
    case 'red':
      return 'error';
    case 'note':
      return 'default';
    default:
      return 'default';
  }
};

const MetricDefinitionCard: React.FC<MetricDefinitionCardProps> = ({ 
  metric, 
  isExpanded: defaultExpanded = false 
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);

  return (
    <Card 
      className="mb-4 hover:shadow-lg transition-shadow duration-200"
      bodyStyle={{ padding: 0 }}
    >
      <div 
        className="p-4 cursor-pointer flex items-start justify-between"
        onClick={() => setIsExpanded(!isExpanded)}
      >
        <div className="flex-1">
          <h3 className="text-lg font-semibold text-gray-800 mb-1">
            {metric.name}
            {metric.unit && (
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({metric.unit})
              </span>
            )}
          </h3>
          {metric.definition && <p className="text-gray-600">{metric.definition}</p>}
          {metric.isClickable && (
            <Tag color="blue" className="mt-2">
              <span className="text-xs">Clickable</span>
            </Tag>
          )}
        </div>
        <div className="ml-4 mt-1">
          {isExpanded ? (
            <ChevronUp className="h-5 w-5 text-gray-400" />
          ) : (
            <ChevronDown className="h-5 w-5 text-gray-400" />
          )}
        </div>
      </div>

      {isExpanded && (
        <div className="border-t border-gray-200 px-4 py-4 bg-gray-50">
          {/* Calculation */}
          {(metric.calculation || metric.formula) && (
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <Calculator className="h-4 w-4 text-purple-600 mr-2" />
                <h4 className="font-medium text-gray-700">Calculation</h4>
              </div>
              <div className="bg-white p-3 rounded border border-gray-200">
                <code className="text-sm text-gray-800">
                  {metric.calculation || metric.formula}
                </code>
              </div>
            </div>
          )}

          {/* Thresholds */}
          {metric.thresholds && metric.thresholds.length > 0 && (
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <Target className="h-4 w-4 text-purple-600 mr-2" />
                <h4 className="font-medium text-gray-700">Performance Thresholds</h4>
              </div>
              <div className="space-y-2">
                {metric.thresholds.map((threshold, index) => (
                  <div key={index} className="flex items-start">
                    <Tag color={getThresholdColor(threshold.level)} className="mr-2">
                      {threshold.level.toUpperCase()}
                    </Tag>
                    <div className="flex-1">
                      <span className="font-medium text-gray-700">
                        {threshold.condition}
                      </span>
                      {threshold.description && (
                        <span className="text-gray-500 ml-2">
                          - {threshold.description}
                        </span>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Business Context */}
          {metric.businessContext && (
            <div className="mb-4">
              <div className="flex items-center mb-2">
                <Info className="h-4 w-4 text-purple-600 mr-2" />
                <h4 className="font-medium text-gray-700">Business Context</h4>
              </div>
              <p className="text-gray-600">{metric.businessContext}</p>
            </div>
          )}

          {/* Notes */}
          {metric.notes && metric.notes.length > 0 && (
            <div>
              <h4 className="font-medium text-gray-700 mb-2">Additional Notes</h4>
              <ul className="list-disc list-inside text-gray-600 space-y-1">
                {metric.notes.map((note, index) => (
                  <li key={index} className="text-sm">{note}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}
    </Card>
  );
};

export default MetricDefinitionCard;