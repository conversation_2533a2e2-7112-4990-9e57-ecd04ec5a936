import React from 'react';
import { Card } from 'antd';

const ThresholdLegend: React.FC = () => {
  return (
    <Card title="Performance Thresholds" className="mb-8">
      <div className="space-y-2">
        <div className="flex items-center">
          <div className="w-6 h-6 rounded mr-3" style={{ backgroundColor: '#4ADE80' }} />
          <span className="font-medium">Green - Meeting or Exceeding Target</span>
        </div>
        <div className="flex items-center">
          <div className="w-6 h-6 rounded mr-3" style={{ backgroundColor: '#FBBF24' }} />
          <span className="font-medium">Yellow - Slightly Below Target</span>
        </div>
        <div className="flex items-center">
          <div className="w-6 h-6 rounded mr-3" style={{ backgroundColor: '#EF4444' }} />
          <span className="font-medium">Red - Significantly Below Target</span>
        </div>
      </div>
    </Card>
  );
};

export default ThresholdLegend;