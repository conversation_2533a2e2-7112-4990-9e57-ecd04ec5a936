import React from 'react';
import { categorySections } from '../data/metricDefinitions';
import { MetricCategory } from '../types/dictionaryTypes';

interface TableOfContentsProps {
  activeSection?: MetricCategory;
  onSectionClick: (sectionId: MetricCategory) => void;
}

const TableOfContents: React.FC<TableOfContentsProps> = ({
  activeSection,
  onSectionClick,
}) => {
  return (
    <div className="sticky top-4">
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <h3 className="font-semibold text-gray-800 mb-4">Table of Contents</h3>
        <nav className="space-y-2">
          <a
            href="#overview"
            className={`block py-2 px-3 rounded text-sm transition-colors ${
              activeSection === undefined
                ? 'bg-purple-50 text-purple-700 font-medium'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={(e) => {
              e.preventDefault();
              document
                .getElementById('overview')
                ?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            Overview
          </a>
          {/* <a
            href="#property-info"
            className={`block py-2 px-3 rounded text-sm transition-colors ${
              activeSection === 'property-info'
                ? 'bg-purple-50 text-purple-700 font-medium'
                : 'text-gray-600 hover:bg-gray-50'
            }`}
            onClick={(e) => {
              e.preventDefault();
              document
                .getElementById('property-info')
                ?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            Property Information
          </a> */}
          {categorySections.map((section) => (
            <a
              key={section.id}
              href={`#${section.id}`}
              className={`block py-2 px-3 rounded text-sm transition-colors ${
                activeSection === section.id
                  ? 'bg-purple-50 text-purple-700 font-medium'
                  : 'text-gray-600 hover:bg-gray-50'
              }`}
              onClick={(e) => {
                e.preventDefault();
                onSectionClick(section.id as MetricCategory);
                document
                  .getElementById(section.id)
                  ?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              {section.title}
            </a>
          ))}
          {/* <a
            href="#glossary"
            className={`block py-2 px-3 rounded text-sm transition-colors text-gray-600 hover:bg-gray-50`}
            onClick={(e) => {
              e.preventDefault();
              document.getElementById('glossary')?.scrollIntoView({ behavior: 'smooth' });
            }}
          >
            Glossary
          </a> */}
        </nav>
      </div>
    </div>
  );
};

export default TableOfContents;
