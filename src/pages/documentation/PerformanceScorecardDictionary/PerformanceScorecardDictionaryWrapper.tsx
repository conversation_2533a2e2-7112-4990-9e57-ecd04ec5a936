import React, { Suspense } from 'react';
import { Spin } from 'antd';

const PerformanceScorecardDictionary = React.lazy(
  () => import('./PerformanceScorecardDictionary')
);

const PerformanceScorecardDictionaryWrapper: React.FC = () => {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center min-h-screen">
          <Spin size="large" tip="Loading Performance Scorecard Dictionary..." />
        </div>
      }
    >
      <PerformanceScorecardDictionary />
    </Suspense>
  );
};

export default PerformanceScorecardDictionaryWrapper;