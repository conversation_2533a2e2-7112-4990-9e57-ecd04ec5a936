import React, { useEffect, useState } from 'react';
import {
  HEADER_COLOR,
  PAGE_BG,
} from '@/pages/report/Scorecard/constants/scorecardConstants';
import { Card, Divider } from 'antd';
import { BarChart3, Book, FileText } from 'lucide-react';
import MetricDefinitionCard from './components/MetricDefinitionCard';
import SearchableMetrics from './components/SearchableMetrics';
import TableOfContents from './components/TableOfContents';
import ThresholdLegend from './components/ThresholdLegend';
import { categorySections } from './data/metricDefinitions';
import { MetricCategory } from './types/dictionaryTypes';

const PerformanceScorecardDictionary: React.FC = () => {
  const [activeSection, setActiveSection] = useState<
    MetricCategory | undefined
  >();

  useEffect(() => {
    // Scroll to top on component mount
    window.scrollTo(0, 0);
  }, []);

  // const propertyInfoColumns = [
  //   {
  //     title: 'Field',
  //     dataIndex: 'label',
  //     key: 'label',
  //     width: '30%',
  //     render: (text: string) => <span className="font-medium">{text}</span>,
  //   },
  //   {
  //     title: 'Description',
  //     dataIndex: 'description',
  //     key: 'description',
  //     width: '50%',
  //   },
  //   {
  //     title: 'Example',
  //     dataIndex: 'example',
  //     key: 'example',
  //     width: '20%',
  //     render: (text: string) =>
  //       text ? (
  //         <code className="text-sm bg-gray-100 px-2 py-1 rounded">{text}</code>
  //       ) : (
  //         '-'
  //       ),
  //   },
  // ];

  return (
    <div style={{ backgroundColor: PAGE_BG, minHeight: '100vh' }}>
      {/* Header */}
      <div
        style={{ backgroundColor: HEADER_COLOR }}
        className="text-white py-8 px-6 mb-8"
      >
        <div className="max-w-7xl mx-auto">
          <div className="flex items-center mb-4">
            <Book className="h-8 w-8 mr-3" />
            <h1 className="text-3xl font-bold">
              Property Performance Scorecard Dictionary
            </h1>
          </div>
          <p className="text-lg opacity-90">
            Comprehensive guide to understanding scorecard metrics and
            calculations
          </p>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-6 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Table of Contents - Sidebar */}
          <div className="lg:col-span-1 hidden lg:block">
            <TableOfContents
              activeSection={activeSection}
              onSectionClick={setActiveSection}
            />
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Search */}
            <Card className="mb-8 shadow-sm">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <FileText className="h-5 w-5 mr-2 text-purple-600" />
                Quick Metric Search
              </h2>
              <SearchableMetrics />
            </Card>

            {/* Overview Section */}
            <section id="overview" className="mb-12 mt-4">
              <Card className="shadow-sm">
                <h2 className="text-2xl font-semibold mb-4">Overview</h2>
                <div className="prose max-w-none text-gray-600">
                  <p className="mb-4">
                    The Property Performance Scorecard provides a comprehensive
                    view of property operations across multiple dimensions. This
                    dictionary explains each metric, its calculation
                    methodology, performance thresholds, and business
                    significance.
                  </p>
                  <h3 className="text-lg font-semibold mt-6 mb-3">
                    How to Use This Guide
                  </h3>
                  <ul className="list-disc list-inside space-y-2">
                    <li>
                      Use the search feature to quickly find specific metrics
                    </li>
                    <li>
                      Click on metric cards to expand detailed information
                    </li>
                    <li>
                      Reference the color coding legend to understand
                      performance indicators
                    </li>
                    <li>
                      Review threshold criteria to understand target performance
                      levels
                    </li>
                  </ul>
                </div>
              </Card>
            </section>

            {/* Threshold Legend */}
            <section className="mb-12">
              <h2 className="text-2xl font-semibold mb-6 flex items-center">
                <BarChart3 className="h-6 w-6 mr-2 text-purple-600" />
                Understanding Performance Indicators
              </h2>
              <ThresholdLegend />
            </section>

            <Divider />

            {/* Property Information Section */}
            {/* <section id="property-info" className="mb-12">
              <Card className="shadow-sm">
                <h2 className="text-2xl font-semibold mb-6">
                  Property Information Fields
                </h2>
                <p className="text-gray-600 mb-6">
                  The Property Information section displays key identifying and
                  descriptive data about the property. This information provides
                  context for all performance metrics.
                </p>
                <Table
                  dataSource={propertyInfoFields}
                  columns={propertyInfoColumns}
                  pagination={false}
                  size="middle"
                  className="property-info-table"
                />
              </Card>
            </section> */}

            {/* Metric Categories */}
            {categorySections.map((section) => (
              <section key={section.id} id={section.id} className="mb-12">
                <Card className="shadow-sm">
                  <h2 className="text-2xl font-semibold mb-2">
                    {section.title}
                  </h2>
                  <p className="text-gray-600 mb-6">{section.description}</p>

                  <div className="space-y-4">
                    {section.metrics.map((metric) => (
                      <MetricDefinitionCard key={metric.id} metric={metric} />
                    ))}
                  </div>
                </Card>
              </section>
            ))}

            {/* Glossary */}
            {/* <section id="glossary" className="mb-12">
              <Card className="shadow-sm">
                <h2 className="text-2xl font-semibold mb-6">
                  Glossary of Terms
                </h2>
                <dl className="space-y-4">
                  <div>
                    <dt className="font-semibold text-gray-800">
                      GRI (Gross Rental Income)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Total rental income before any deductions or concessions
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      NOI (Net Operating Income)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Total income minus operating expenses, excluding debt
                      service and capital expenditures
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      MTM (Month-to-Month)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Lease agreements that renew automatically each month
                      without a fixed term
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      YTD (Year-to-Date)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Period from the beginning of the current year to the
                      present date
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      YoY (Year-over-Year)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Comparison of current period metrics to the same period in
                      the previous year
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      T30 (Trailing 30 Days)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Metrics calculated over the most recent 30-day period
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      CapEx (Capital Expenditure)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Funds used to acquire, upgrade, or maintain physical
                      assets
                    </dd>
                  </div>
                  <div>
                    <dt className="font-semibold text-gray-800">
                      OpEx (Operating Expenses)
                    </dt>
                    <dd className="text-gray-600 ml-4">
                      Ongoing costs for running the property, excluding capital
                      expenditures
                    </dd>
                  </div>
                </dl>
              </Card>
            </section> */}
          </div>
        </div>
      </div>

      <style>{`
        .property-info-table .ant-table-thead > tr > th {
          background-color: #f3f4f6;
          font-weight: 600;
        }
      `}</style>
    </div>
  );
};

export default PerformanceScorecardDictionary;
