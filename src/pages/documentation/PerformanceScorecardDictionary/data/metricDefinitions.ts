import { CategorySection, MetricDefinition } from '../types/dictionaryTypes';

const occupancyLeasingMetrics: MetricDefinition[] = [
  {
    name: 'Occupancy Non Rev',
    calculation: '(Occupied No Notice + Notice Rented + Notice Unrented+Admin+Down+Model)/Total Units',
    thresholds: [
      { level: 'green', condition: '95% to 97%' },
      { level: 'yellow', condition: '90% to 94.9%; Greater than 97%' },
      { level: 'red', condition: 'Less than 90%' },
    ],
  },
  {
    name: 'Submarket Occupancy',
    calculation: 'Submarket Occupancy',
    thresholds: [
      { level: 'green', condition: '95% to 97%' },
      { level: 'yellow', condition: '90% to 94.9%; Greater than 97%' },
      { level: 'red', condition: 'Less than 90%' },
    ],
  },
  {
    name: 'Variance to Submarket',
    calculation: 'Variance of Occupancy to Market Occupancy',
    thresholds: [
      { level: 'green', condition: 'Greater than 0%' },
      { level: 'red', condition: 'Less than 0%' },
    ],
  },
  {
    name: 'Occupancy Trend',
    calculation: '(Total Units - (Vacant Unrented + Notice Unrented))/Total Units',
    thresholds: [
      { level: 'green', condition: '92.1 to 100%' },
      { level: 'yellow', condition: '90.1% to 92%' },
      { level: 'red', condition: 'Less than 90%' },
    ],
  },
  {
    name: 'Variance to Occupancy',
    calculation: 'Occupancy Trend - Occupancy',
    thresholds: [
      { level: 'green', condition: '-3% to 3%' },
      { level: 'yellow', condition: '3.1% to 5%; -3.1% to -5%' },
      { level: 'red', condition: 'Greater than 5%; Less than -5%' },
    ],
  },
  {
    name: 'Trend Gain/Loss',
    calculation: '(Gain/Loss)/Total Units',
    thresholds: [
      { level: 'green', condition: '-1% to 1%' },
      { level: 'yellow', condition: '1.1% to 2.9%; -1.1% to -2.9%' },
      { level: 'red', condition: 'Greater than 3%; Less than -3%' },
    ],
  },
  {
    name: 'Gain/Loss',
    calculation: 'Cancel notices-Notices + Applications - Cancels - Denials',
    thresholds: [
      { level: 'note', condition: 'Refer to Trends % Gain/Loss' },
    ],
  },
  {
    name: 'Occupancy Trend T30 Variance',
    calculation: 'Occupancy Trend at end Date - Occupancy Trend 30 days prior',
    thresholds: [
      { level: 'green', condition: 'Greater than 0%' },
      { level: 'red', condition: 'Less than 0%' },
    ],
  },
  {
    name: 'Units Available',
    calculation: 'Vacant Unrented + Notice Unrented',
    thresholds: [],
  },
  {
    name: 'Vacant Units',
    calculation: 'Number of Units Vacant',
    thresholds: [],
  },
  {
    name: 'Aged Vacant Units',
    calculation: 'Number of Units that have been vacant more than 30 days',
    thresholds: [
      { level: 'red', condition: 'Greater than 2% of units' },
    ],
  },
  {
    name: 'Avg Aged Days Vacant',
    calculation: 'Average days vacant for all units vacant more than 30 days as of report date',
    thresholds: [
      { level: 'red', condition: 'Greater than 30 days' },
    ],
  },
  {
    name: 'Shows T30',
    calculation: 'First Unique Prospect Shows',
    thresholds: [
      { level: 'green', condition: 'Greater than 12% of total units' },
      { level: 'red', condition: 'Less 12% of total units' },
    ],
  },
];

const rentalMetrics: MetricDefinition[] = [
  {
    name: 'New Net In Place Rent',
    calculation: 'In Place Rent for all leases on their first lease.',
    thresholds: [],
    isClickable: true,
  },
  {
    name: 'New Net In Place Rent YoY Change',
    calculation: '(New Net In Place Rent - New Net In Place Rent Same Date Prior Year) /New Net In Place Rent Same Date Prior Year',
    thresholds: [
      { level: 'green', condition: 'Greater than 5%' },
      { level: 'yellow', condition: '-1% to 1%' },
      { level: 'red', condition: 'Less than -5%' },
    ],
  },
  {
    name: 'Renewal Net In Place Rent',
    calculation: 'Net In Place Rent for all current Renewal leases',
    thresholds: [],
    isClickable: true,
  },
  {
    name: 'Renewal Net In Place Rent YoY Change',
    calculation: '(Renewal Net In Place Rent - Renewal Net In Place Rent Same Date Prior Year) /Renewal Net In Place Rent Same Date Prior Year',
    thresholds: [
      { level: 'green', condition: 'Greater than 5%' },
      { level: 'yellow', condition: '-1% to 1%' },
      { level: 'red', condition: 'Less than -5%' },
    ],
  },
  {
    name: 'Net In Place Rent',
    calculation: 'Net In Place Rent for all Units',
    thresholds: [],
    isClickable: true,
  },
  {
    name: 'Net In Place Rent YoY Change',
    calculation: '(Net In Place Rent - Net In Place Rent Same Date Prior Year)/Net In Place Rent Same Date Prior Year',
    thresholds: [
      { level: 'green', condition: 'Greater than 5%' },
      { level: 'yellow', condition: '-1% to 1%' },
      { level: 'red', condition: 'Less than -5%' },
    ],
  },
  {
    name: 'YTD Renewal Conversion',
    calculation: '% of the leases set to expire YTD that have renewed.',
    thresholds: [
      { level: 'green', condition: '50.1% to 60%' },
      { level: 'yellow', condition: '40% to 50%' },
      { level: 'red', condition: 'Less than 40%' },
    ],
  },
  {
    name: 'MTM',
    calculation: 'Lease expired, no notice given and resident has not moved out.',
    thresholds: [],
  },
];

const financialMetrics: MetricDefinition[] = [
  {
    name: 'Rental Income',
    calculation: "Variance of accounts '401'-'405', 40653-40655.",
    thresholds: [
      { level: 'green', condition: 'More than or equal to 2%' },
      { level: 'yellow', condition: '-1.9% to 1.9%' },
      { level: 'red', condition: 'Less than or equal to -2%' },
    ],
  },
  {
    name: 'Total Income',
    calculation: "Variance of all accounts that start with '4'",
    thresholds: [
      { level: 'green', condition: 'More than or equal to 2%' },
      { level: 'yellow', condition: '-1.9% to 1.9%' },
      { level: 'red', condition: 'Less than or equal to -2%' },
    ],
  },
  {
    name: 'Controllable Op Exp',
    calculation: "Variance of accounts '60'-'63'",
    thresholds: [
      { level: 'green', condition: 'Less than or equal to -3%' },
      { level: 'yellow', condition: '-2.9% to 2.9%' },
      { level: 'red', condition: 'Greater than or equal to 3%' },
    ],
  },
  {
    name: 'Total Opex',
    calculation: "Variance of accounts '6'",
    thresholds: [
      { level: 'green', condition: 'Less than or equal to -3%' },
      { level: 'yellow', condition: '-2.9% to 2.9%' },
      { level: 'red', condition: 'Greater than or equal to 3%' },
    ],
  },
  {
    name: 'NOI',
    calculation: "Variance of accounts '4'-'6'",
    thresholds: [
      { level: 'green', condition: 'Greater than or equal to 2%' },
      { level: 'yellow', condition: '-1.9% to 1.9%' },
      { level: 'red', condition: 'Less than or equal to -2%' },
    ],
  },
  {
    name: 'Controllable NOI',
    calculation: "Variance of accounts '40'-'63'",
    thresholds: [
      { level: 'green', condition: 'Greater than or equal to 2%' },
      { level: 'yellow', condition: '-1.9% to 1.9%' },
      { level: 'red', condition: 'Less than or equal to -2%' },
    ],
  },
];

const collectionsMetrics: MetricDefinition[] = [
  {
    name: 'Collections % MTD',
    calculation: "Sum of Receipts as of end date as % of 'Rent' charges this month. Charge code is 'Rent'.",
    thresholds: [
      { level: 'green', condition: 'Greater than or equal to 98%' },
      { level: 'yellow', condition: '85% to 97.9%' },
      { level: 'red', condition: 'Less than 85%' },
    ],
  },
  {
    name: 'Bad Debt W/O (Net) as % of GRI (Market Rents) YTD',
    calculation: "YTD Net bad debt loss ('*********, *********') /GRI ('4021, 4022, 4023, 4024')",
    thresholds: [
      { level: 'green', condition: '-1 to 1%' },
      { level: 'yellow', condition: '-2% to -0.9%' },
      { level: 'red', condition: 'Less than -2%' },
    ],
  },
  {
    name: 'Collection Recovery Ratio (In House & Agency)',
    calculation: 'Bad Debt Recovery (404251000,404252000)/Bad Debt Loss(*********,*********)',
    thresholds: [
      { level: 'green', condition: 'If Bad Debt = $0; Greater than or equal to 15%' },
      { level: 'yellow', condition: '10 to 14.9%' },
      { level: 'red', condition: 'Less than 10%' },
    ],
  },
];

const facilitiesMetrics: MetricDefinition[] = [
  {
    name: 'Average Unit Turn Time t90',
    calculation: 'Average days from move out to ready',
    thresholds: [
      { level: 'green', condition: 'Less than or equal to 10 days' },
      { level: 'yellow', condition: '10.1 to 12.9 days' },
      { level: 'red', condition: 'Greater than or equal to 13 days' },
    ],
  },
  {
    name: 'Units with Repeat Service Tickets Within 30 Days',
    calculation: 'Number of units with repeat tickets within 30 days. Repeat is defined as a previous ticket of the same category within 30 days.',
    thresholds: [
      { level: 'green', condition: 'Less than 2% of total units' },
      { level: 'yellow', condition: '2% to 5% of total units' },
      { level: 'red', condition: 'Greater than 5.1% of total units' },
    ],
  },
  {
    name: '% Tickets >72 Hours to Close T30',
    calculation: 'Tickets that took >72 hours to close/tickets opened in the last 30 days.',
    thresholds: [
      { level: 'green', condition: 'Less than or equal to 5%' },
      { level: 'yellow', condition: '5.1% to 10%' },
      { level: 'red', condition: 'Greater than 10%' },
    ],
  },
  {
    name: 'Capital Execution',
    calculation: "Sum of YTD  CapEx actuals /YTD sum of CapEx budget. Refer to Capital row for GL's.",
    thresholds: [
      { level: 'green', condition: '85% to 115%' },
      { level: 'yellow', condition: '70% to 84.9%; 115.1% to 130%' },
      { level: 'red', condition: 'Less than 70%; Greater than 130%' },
    ],
  },
  {
    name: 'YTD Turn Cost',
    calculation: "Sum of all '617' accounts/sum of YTD move outs",
    thresholds: [],
  },
];

const additionalMetrics: MetricDefinition[] = [
  {
    name: 'J Turner',
    calculation: 'Most Recent J Turner Score',
    thresholds: [],
  },
  {
    name: 'Google',
    calculation: 'Most Recent Google Rating',
    thresholds: [],
  },
];

export const categorySections: CategorySection[] = [
  {
    id: 'occupancy-leasing',
    title: 'Occupancy & Leasing',
    metrics: occupancyLeasingMetrics,
  },
  {
    id: 'rental-metrics',
    title: 'Net In Place Effective Rents, Year Over Year Change, Renewal Conversion',
    metrics: rentalMetrics,
  },
  {
    id: 'financial-performance',
    title: 'Financial Performance to YTD Budget',
    metrics: financialMetrics,
  },
  {
    id: 'collections',
    title: 'Collections, Bad Debt Write off & Recovery',
    metrics: collectionsMetrics,
  },
  {
    id: 'facilities',
    title: 'Capital & Facilities Maintenance',
    metrics: facilitiesMetrics,
  },
  {
    id: 'reputation',
    title: 'Reputation Management',
    metrics: additionalMetrics,
  },
];