export type ThresholdLevel = 'green' | 'yellow' | 'red' | 'note';

export interface Threshold {
  level: ThresholdLevel;
  condition: string;
}

export interface MetricDefinition {
  id?: string;
  name: string;
  category?: MetricCategory;
  definition?: string;
  calculation?: string;
  formula?: string;
  thresholds?: Threshold[];
  businessContext?: string;
  notes?: string[];
  unit?: string;
  isClickable?: boolean;
  clickAction?: string;
}

export type MetricCategory = 
  | 'property-info'
  | 'occupancy-leasing'
  | 'rental-metrics'
  | 'financial-performance'
  | 'turn-cost'
  | 'reputation'
  | 'collections'
  | 'facilities';

export interface CategorySection {
  id: string;
  title: string;
  description?: string;
  icon?: string;
  metrics: MetricDefinition[];
}

export interface PropertyInfoField {
  field: string;
  label: string;
  description: string;
  dataType?: string;
  example?: string;
}

export interface PerformanceScore {
  icon: string;
  meaning: string;
  criteria: string;
}

export interface ColorLegend {
  color: string;
  hexCode: string;
  usage: string;
  threshold?: string;
}

export interface SearchResult {
  metric: MetricDefinition;
  matchType: 'name' | 'definition' | 'category';
  relevance: number;
}