import React from 'react';
import { GroupedLineItemDetails } from '../hooks/useLineItemDetails';
import {
  BaseCategoryTotals,
  CategoryData,
  PTDCategoryData,
  PTDCategoryTotals,
  StatementColumn,
  ThousandCategoryData,
  ThousandCategoryTotals,
} from '../types/statementTypes';
import ItemRowsRenderer from './ItemRowsRenderer';
import SectionRenderer from './SectionRenderer';
import TotalRowRenderer from './TotalRowRenderer';

interface StatementTableBodyProps {
  categories: CategoryData[] | PTDCategoryData[] | ThousandCategoryData[];
  columns: StatementColumn[];
  expandedCategories: Record<string, boolean>;
  expandedLineItems?: Record<string, boolean>;
  lineItemDetails?: GroupedLineItemDetails;
  convertTotalForReportTable: (
    total: BaseCategoryTotals | PTDCategoryTotals | ThousandCategoryTotals,
    categoryTitle?: string,
  ) => Record<string, number | string>;
  formatCellValue: (
    value: number | string,
    columnId: string,
    category?: CategoryData | PTDCategoryData | ThousandCategoryData,
  ) => string;
}

const StatementTableBody: React.FC<StatementTableBodyProps> = ({
  categories,
  columns,
  expandedCategories,
  expandedLineItems,
  lineItemDetails,
  convertTotalForReportTable,
  formatCellValue,
}) => {
  return (
    <tbody className="bg-white">
      {categories.map(
        (
          category: CategoryData | PTDCategoryData | ThousandCategoryData,
          categoryIndex: number,
        ) => (
          <React.Fragment key={category.title}>
            <SectionRenderer
              categoryTitle={category.title}
              columnCount={columns.length}
              categoryIndex={categoryIndex}
            />

            <ItemRowsRenderer
              category={category}
              categoryIndex={categoryIndex}
              columns={columns}
              expandedCategories={expandedCategories}
              expandedLineItems={expandedLineItems}
              lineItemDetails={lineItemDetails}
            />

            <TotalRowRenderer
              category={category}
              categoryIndex={categoryIndex}
              columns={columns}
              convertTotalForReportTable={convertTotalForReportTable}
              formatCellValue={formatCellValue}
            />
          </React.Fragment>
        ),
      )}
    </tbody>
  );
};

export default StatementTableBody;
