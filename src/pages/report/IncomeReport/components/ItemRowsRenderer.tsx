import React from 'react';
import { toggleLineItemExpansion } from '@/slice/incomeReportSlice';
import { Minus, Plus } from 'lucide-react';
import { useDispatch } from 'react-redux';
import { GroupedLineItemDetails } from '../hooks/useLineItemDetails';
import {
  CategoryData,
  PTDCategoryData,
  StatementColumn,
  ThousandCategoryData,
} from '../types/statementTypes';
import { getTableCellStyle } from '../utils/formatting';
import AccountDetailsRenderer from './AccountDetailsRenderer';

interface ItemRowsRendererProps {
  category: CategoryData | PTDCategoryData | ThousandCategoryData;
  categoryIndex: number;
  columns: StatementColumn[];
  expandedCategories: Record<string, boolean>;
  expandedLineItems?: Record<string, boolean>;
  lineItemDetails?: GroupedLineItemDetails;
}

const ItemRowsRenderer: React.FC<ItemRowsRendererProps> = ({
  category,
  categoryIndex,
  columns,
  expandedCategories,
  expandedLineItems,
  lineItemDetails,
}) => {
  const dispatch = useDispatch();

  if (!expandedCategories?.[category.title]) return null;

  return (
    <>
      {category.items.map(
        (
          item:
            | CategoryData['items'][0]
            | PTDCategoryData['items'][0]
            | ThousandCategoryData['items'][0],
          itemIndex: number,
        ) => {
          const lineItemKey = `${category.title}-${item.name}`;
          const isExpanded = expandedLineItems?.[lineItemKey] || false;

          return (
            <React.Fragment key={`${categoryIndex}-${itemIndex}`}>
              <tr
                className={`border-b border-[#DEEFFF] ${
                  itemIndex % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                } ${expandedLineItems && lineItemDetails ? 'cursor-pointer hover:bg-gray-50' : ''}`}
                onClick={() =>
                  expandedLineItems && lineItemDetails
                    ? dispatch(toggleLineItemExpansion(lineItemKey))
                    : undefined
                }
              >
                {columns.map((column, colIndex) => (
                  <td
                    key={`${categoryIndex}-${itemIndex}-${column.id}`}
                    className={`px-2 py-0 
                      ${
                        column.id !== 'name' &&
                        column.id !== 'property' &&
                        column.id !== 'propertyAlias' &&
                        column.id !== 'rpm'
                          ? 'text-right'
                          : 'text-left'
                      } ${column.className || ''}`}
                    style={getTableCellStyle(colIndex)}
                  >
                    {column.id === 'name' ||
                    column.id === 'property' ||
                    column.id === 'propertyAlias' ? (
                      <div className="flex items-center gap-2">
                        {expandedLineItems && lineItemDetails && isExpanded ? (
                          <Minus className="h-4 w-4 text-gray-500" />
                        ) : expandedLineItems && lineItemDetails ? (
                          <Plus className="h-4 w-4 text-gray-500" />
                        ) : null}
                        {column.accessor(item)}
                      </div>
                    ) : (
                      column.accessor(item)
                    )}
                  </td>
                ))}
              </tr>
              {isExpanded && expandedLineItems && lineItemDetails && (
                <AccountDetailsRenderer
                  lineItemName={item.name}
                  categoryIndex={categoryIndex}
                  itemIndex={itemIndex}
                  columns={columns}
                  lineItemDetails={lineItemDetails}
                />
              )}
            </React.Fragment>
          );
        },
      )}
    </>
  );
};

export default ItemRowsRenderer;
