import React from 'react';

interface StatementTableFooterProps {
  columnCount: number;
}

const StatementTableFooter: React.FC<StatementTableFooterProps> = ({
  columnCount,
}) => {
  return (
    <tbody>
      <tr>
        {Array.from({ length: columnCount }).map((_, index) => (
          <td
            key={index}
            className="h-0 border-none"
            style={
              index === 0
                ? {}
                : { borderBottom: '2px solid black' }
            }
          />
        ))}
      </tr>
    </tbody>
  );
};

export default StatementTableFooter;