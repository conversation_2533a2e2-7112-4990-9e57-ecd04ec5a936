import React, { useMemo } from 'react';
import { expandAllLineItems, collapseAllLineItems } from '@/slice/incomeReportSlice';
import { ChevronDown, ChevronUp } from 'lucide-react';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@/store';
import {
  CategoryData,
  PTDCategoryData,
  ThousandCategoryData,
} from '../types/statementTypes';

interface ExpandCollapseAllButtonProps {
  categories: CategoryData[] | PTDCategoryData[] | ThousandCategoryData[];
}

const ExpandCollapseAllButton: React.FC<ExpandCollapseAllButtonProps> = ({
  categories,
}) => {
  const dispatch = useDispatch();
  const expandedLineItems = useSelector(
    (state: RootState) => state.incomeReport.expandedLineItems,
  );

  const allLineItemKeys = useMemo(() => {
    const keys: string[] = [];
    categories.forEach((category) => {
      category.items.forEach((item) => {
        keys.push(`${category.title}-${item.name}`);
      });
    });
    return keys;
  }, [categories]);

  const allExpanded = useMemo(() => {
    return allLineItemKeys.every((key) => expandedLineItems[key] === true);
  }, [allLineItemKeys, expandedLineItems]);

  const handleToggle = () => {
    if (allExpanded) {
      dispatch(collapseAllLineItems());
    } else {
      dispatch(expandAllLineItems(allLineItemKeys));
    }
  };

  return (
    <button
      onClick={handleToggle}
      className="flex items-center gap-2 px-4 py-2 text-sm font-medium text-[#43298F] bg-white border border-[#43298F] rounded-lg hover:bg-[#F4F4FF] transition-colors"
    >
      {allExpanded ? (
        <>
          <ChevronUp className="h-4 w-4" />
          Collapse All
        </>
      ) : (
        <>
          <ChevronDown className="h-4 w-4" />
          Expand All
        </>
      )}
    </button>
  );
};

export default ExpandCollapseAllButton;