import React from 'react';
import {
  getSectionHeaderText,
  getSectionRowStyle,
  shouldShowSectionHeader,
} from '../utils/formatting';

interface SectionRendererProps {
  categoryTitle: string;
  columnCount: number;
  categoryIndex: number;
}

const SectionRenderer: React.FC<SectionRendererProps> = ({
  categoryTitle,
  columnCount,
  categoryIndex,
}) => {
  const renderSectionSpacer = () => (
    <tr>
      {Array.from({ length: columnCount }).map((_, index) => (
        <td
          key={index}
          className="h-4 bg-white border-none"
          style={getSectionRowStyle(index)}
        />
      ))}
    </tr>
  );

  const renderSectionHeader = () => (
    <tr className="bg-[#ffffff] text-black">
      <td className="px-3 py-2 font-bold text-left">
        {getSectionHeaderText(categoryTitle)}
      </td>
      {Array.from({ length: columnCount - 1 }).map((_, index) => (
        <td
          key={index}
          className="px-3 py-2"
          style={getSectionRowStyle(index + 1)}
        />
      ))}
    </tr>
  );

  return (
    <>
      {categoryIndex > 0 && renderSectionSpacer()}
      {shouldShowSectionHeader(categoryTitle) && renderSectionHeader()}
    </>
  );
};

export default SectionRenderer;
