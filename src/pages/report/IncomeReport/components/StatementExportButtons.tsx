import React from 'react';
import { FileDown, FileUp, Search } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface StatementExportButtonsProps {
  onExportExcel: () => void;
  onExportPDF: () => void;
  onDrillThrough?: () => void;
  disabled?: boolean;
  showDrillThrough?: boolean;
}

const StatementExportButtons: React.FC<StatementExportButtonsProps> = ({
  onExportExcel,
  onExportPDF,
  onDrillThrough,
  disabled = false,
  showDrillThrough = false,
}) => {
  return (
    <div className="flex gap-2">
      {showDrillThrough && onDrillThrough && (
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={onDrillThrough}
          disabled={disabled}
        >
          <Search className="mr-2 h-4 w-4" />
          Drill Through
        </Button>
      )}
      <Button
        variant="outline"
        className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
        onClick={onExportExcel}
        disabled={disabled}
      >
        <FileUp className="mr-2 h-4 w-4" />
        Export Excel
      </Button>
      <Button
        variant="outline"
        className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
        onClick={onExportPDF}
        disabled={disabled}
      >
        <FileDown className="mr-2 h-4 w-4" />
        Export PDF
      </Button>
    </div>
  );
};

export default StatementExportButtons;
