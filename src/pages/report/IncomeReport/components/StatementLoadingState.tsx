import React from 'react';

interface StatementLoadingStateProps {
  message?: string;
}

const StatementLoadingState: React.FC<StatementLoadingStateProps> = ({
  message = 'Loading report data...',
}) => {
  return (
    <div className="flex h-screen w-full items-center justify-center">
      <div className="text-center">
        <div className="animate-spin h-8 w-8 border-4 border-[#43298F] border-t-transparent rounded-full mx-auto mb-4"></div>
        <p>{message}</p>
      </div>
    </div>
  );
};

export default StatementLoadingState;