import React from 'react';
import { StatementColumn } from '../types/statementTypes';
import { getTableCellStyle } from '../utils/formatting';

interface StatementTableHeaderProps {
  columns: StatementColumn[];
  leftGroupTitle: string;
  rightGroupTitle: string;
  year: string;
}

const StatementTableHeader: React.FC<StatementTableHeaderProps> = ({
  columns,
  leftGroupTitle,
  rightGroupTitle,
  year,
}) => {
  return (
    <thead>
      <tr className="text-white">
        <th className="border-[#F3F4FF]"></th>
        <th
          colSpan={4}
          className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
          style={{
            borderLeft: '2px solid black',
            borderRight: '2px solid black',
          }}
        >
          {leftGroupTitle} {year}
        </th>
        <th
          colSpan={4}
          className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
          style={{
            borderLeft: '2px solid black',
            borderRight: '2px solid black',
          }}
        >
          {rightGroupTitle} {year}
        </th>
      </tr>
      <tr className="bg-[#43298F] text-white">
        {columns.map((column, index) => (
          <th
            key={column.id}
            className={`p-3 text-xs font-medium uppercase tracking-wider border border-black ${
              column.id !== 'name' &&
              column.id !== 'property' &&
              column.id !== 'propertyAlias' &&
              column.id !== 'rpm'
                ? 'text-right'
                : 'text-left'
            } ${column.className || ''}`}
            style={getTableCellStyle(index)}
          >
            {column.header}
          </th>
        ))}
      </tr>
    </thead>
  );
};

export default StatementTableHeader;