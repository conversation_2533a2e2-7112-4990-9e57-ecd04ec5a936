import React from 'react';
import { GroupedLineItemDetails } from '../hooks/useLineItemDetails';
import {
  BaseCategoryTotals,
  CategoryData,
  PTDCategoryData,
  PTDCategoryTotals,
  StatementColumn,
  ThousandCategoryData,
  ThousandCategoryTotals,
} from '../types/statementTypes';
import StatementTableBody from './StatementTableBody';
import StatementTableFooter from './StatementTableFooter';
import StatementTableHeader from './StatementTableHeader';

interface StatementTableProps {
  categories: CategoryData[] | PTDCategoryData[] | ThousandCategoryData[];
  columns: StatementColumn[];
  expandedCategories: Record<string, boolean>;
  expandedLineItems?: Record<string, boolean>;
  lineItemDetails?: GroupedLineItemDetails;
  leftGroupTitle: string;
  rightGroupTitle: string;
  year: string;
  convertTotalForReportTable: (
    total: BaseCategoryTotals | PTDCategoryTotals | ThousandCategoryTotals,
    categoryTitle?: string,
  ) => Record<string, number | string>;
  formatCellValue: (
    value: number | string,
    columnId: string,
    category?: CategoryData | PTDCategoryData | ThousandCategoryData,
  ) => string;
}

const StatementTable: React.FC<StatementTableProps> = ({
  categories,
  columns,
  expandedCategories,
  expandedLineItems,
  lineItemDetails,
  leftGroupTitle,
  rightGroupTitle,
  year,
  convertTotalForReportTable,
  formatCellValue,
}) => {
  if (categories.length === 0) {
    return (
      <div className="text-center p-8 text-gray-500">
        No data available for the selected filters
      </div>
    );
  }

  return (
    <div className="border border-[#F3F4FF] overflow-hidden">
      <table className="min-w-full border-collapse">
        <StatementTableHeader
          columns={columns}
          leftGroupTitle={leftGroupTitle}
          rightGroupTitle={rightGroupTitle}
          year={year}
        />
        <StatementTableBody
          categories={categories}
          columns={columns}
          expandedCategories={expandedCategories}
          expandedLineItems={expandedLineItems}
          lineItemDetails={lineItemDetails}
          convertTotalForReportTable={convertTotalForReportTable}
          formatCellValue={formatCellValue}
        />
        <StatementTableFooter columnCount={columns.length} />
      </table>
    </div>
  );
};

export default StatementTable;
