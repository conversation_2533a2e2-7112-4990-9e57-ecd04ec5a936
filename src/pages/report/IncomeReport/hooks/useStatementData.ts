import { useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { RootState } from '@/store';
import { setError, setLoading } from '@/slice/incomeReportSlice';
import { createAPIRequestBody } from '../utils/dataTransformers';

interface UseStatementDataProps<T> {
  apiFunction: (requestBody: any) => Promise<{ data: any[] }>;
  formatFunction: (data: any[]) => T;
  dispatchFunction: (data: T) => { type: string; payload: T };
}

export const useStatementData = <T>({
  apiFunction,
  formatFunction,
  dispatchFunction,
}: UseStatementDataProps<T>) => {
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => state.incomeReport.filters);

  useEffect(() => {
    const fetchData = async () => {
      dispatch(setLoading(true));
      try {
        const requestBody = createAPIRequestBody(filters);
        const response = await apiFunction(requestBody);

        if (response) {
          if (!response.data || response.data.length === 0) {
            toast.warning('No data found for the selected filters');
          } else {
            const formattedData = formatFunction(response.data);
            dispatch(dispatchFunction(formattedData));
          }
        }
      } catch (error) {
        console.error('Error fetching statement data:', error);
        dispatch(setError('Failed to fetch statement data'));
      } finally {
        dispatch(setLoading(false));
      }
    };

    fetchData();
  }, [filters, dispatch, apiFunction, formatFunction, dispatchFunction]);
};

export const useStatementDataWithState = <T, D>(
  apiFunction: (requestBody: any) => Promise<{ data: D[] }>,
  formatFunction: (data: D[]) => T
) => {
  const dispatch = useDispatch();
  const filters = useSelector((state: RootState) => state.incomeReport.filters);
  const [data, setData] = useState<T | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      dispatch(setLoading(true));
      try {
        const requestBody = createAPIRequestBody(filters);
        const response = await apiFunction(requestBody);

        if (response) {
          if (!response.data || response.data.length === 0) {
            toast.warning('No data found for the selected filters');
            setData(null);
          } else {
            const formattedData = formatFunction(response.data);
            setData(formattedData);
          }
        }
      } catch (error) {
        console.error('Error fetching statement data:', error);
        dispatch(setError('Failed to fetch statement data'));
      } finally {
        dispatch(setLoading(false));
      }
    };

    fetchData();
  }, [filters, dispatch, apiFunction, formatFunction]);

  return data;
};