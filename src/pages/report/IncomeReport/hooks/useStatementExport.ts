import { useCallback } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { sortCategories } from '../utils/sorting';
import {
  CategoryData,
  PTDCategoryData,
  ThousandCategoryData,
} from '../types/statementTypes';

interface UseStatementExportProps {
  data: CategoryData[] | PTDCategoryData[] | ThousandCategoryData[];
  exportToExcel: (
    data: any[],
    lastClosedMonth: string,
    expandedCategories: Record<string, boolean>,
    filters: any
  ) => void;
  exportToPDF: (
    data: any[],
    lastClosedMonth: string,
    expandedCategories: Record<string, boolean>,
    filters: any
  ) => void;
}

export const useStatementExport = ({
  data,
  exportToExcel,
  exportToPDF,
}: UseStatementExportProps) => {
  const { lastClosedMonth, expandedCategories, filters } = useSelector(
    (state: RootState) => ({
      lastClosedMonth: state.incomeReport.lastClosedMonth,
      expandedCategories: state.incomeReport.expandedCategories,
      filters: state.incomeReport.filters,
    })
  );

  const handleExportToExcel = useCallback(() => {
    const sortedData = sortCategories(data);
    exportToExcel(sortedData, lastClosedMonth, expandedCategories, filters);
  }, [data, exportToExcel, lastClosedMonth, expandedCategories, filters]);

  const handleExportToPDF = useCallback(() => {
    const sortedData = sortCategories(data);
    exportToPDF(sortedData, lastClosedMonth, expandedCategories, filters);
  }, [data, exportToPDF, lastClosedMonth, expandedCategories, filters]);

  return {
    handleExportToExcel,
    handleExportToPDF,
  };
};