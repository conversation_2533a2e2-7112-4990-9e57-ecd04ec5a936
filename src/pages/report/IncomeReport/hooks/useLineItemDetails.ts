import { useEffect, useState, useMemo } from 'react';
import { useSelector } from 'react-redux';
import { RootState } from '@/store';
import { getYTDDrillThrough } from '@/api/statementOfOperationsApi';

export interface LineItemDetail {
  ExpandTotal: string;
  Total: string;
  acctCode: string;
  AcctCodeAndDesc: string;
  PTD_Actual: number;
  PTD_Budget: number;
  PTD_Variance: number;
  PTD_Variance_Perct: number;
  YTD_Actual: number;
  YTD_Budget: number;
  YTD_Variance: number;
  YTD_Variance_Perct: number;
  Actual?: number | null;
  budget?: number | null;
  variance?: number | null;
  Variance_Pert?: number | null;
  forecast?: number | null;
  budget1?: number | null;
  Variance1?: number | null;
  FY_Variance_Pert?: number | null;
}

export interface GroupedLineItemDetails {
  [lineItemName: string]: LineItemDetail[];
}

export const useLineItemDetails = () => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [rawData, setRawData] = useState<LineItemDetail[]>([]);
  
  const filters = useSelector((state: RootState) => state.incomeReport.filters);

  useEffect(() => {
    const fetchLineItemDetails = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Convert month names to numbers
        const monthNumbers = filters.month.map(monthName => {
          const monthMap: Record<string, string> = {
            'January': '1',
            'February': '2',
            'March': '3',
            'April': '4',
            'May': '5',
            'June': '6',
            'July': '7',
            'August': '8',
            'September': '9',
            'October': '10',
            'November': '11',
            'December': '12'
          };
          return monthMap[monthName] || '1';
        });

        const response = await getYTDDrillThrough({
          year: filters.year,
          month: monthNumbers,
          department: filters.department.length > 0 ? filters.department : null,
          businessType: filters.businessType.length > 0 ? filters.businessType : null,
          marketleader: filters.marketLeader.length > 0 ? filters.marketLeader : null,
          adminBu: filters.adminBU.length > 0 ? filters.adminBU : null,
        });
        
        setRawData(response.data || []);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to fetch line item details');
      } finally {
        setLoading(false);
      }
    };

    fetchLineItemDetails();
  }, [filters]);

  // Group data by the "Total" field
  const groupedData = useMemo<GroupedLineItemDetails>(() => {
    return rawData.reduce((acc, item) => {
      const lineItemName = item.Total;
      if (!acc[lineItemName]) {
        acc[lineItemName] = [];
      }
      acc[lineItemName].push(item);
      return acc;
    }, {} as GroupedLineItemDetails);
  }, [rawData]);

  return {
    loading,
    error,
    lineItemDetails: groupedData,
  };
};