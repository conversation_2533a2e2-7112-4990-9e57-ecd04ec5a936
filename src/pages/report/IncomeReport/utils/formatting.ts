import {
  BaseCategoryTotals,
  PTDCategoryTotals,
  ThousandCategoryTotals,
} from '../types/statementTypes';

export const formatCurrency = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toLocaleString('en-US', {
    maximumFractionDigits: 0,
  });
  return value < 0 ? `($${formatted})` : `$${formatted}`;
};

export const formatNumber = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toLocaleString('en-US', {
    maximumFractionDigits: 0,
  });
  return value < 0 ? `(${formatted})` : formatted;
};

export const formatPercentage = (value: number, decimals: number = 1): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toLocaleString('en-US', { maximumFractionDigits: decimals });
  return value < 0 ? `(${formatted}%)` : `${formatted}%`;
};

export const formatVariance = (value: number, isUnits: boolean = false): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toLocaleString('en-US', {
    maximumFractionDigits: 0,
  });

  if (isUnits) {
    return value < 0 ? `(${formatted})` : formatted;
  } else {
    return value < 0 ? `($${formatted})` : `$${formatted}`;
  }
};

export const convertYTDTotalForReportTable = (
  totals: BaseCategoryTotals,
  categoryTitle?: string
): Record<string, number | string> => {
  const isMarginCategory =
    categoryTitle &&
    (categoryTitle === 'EBITDA MARGIN' ||
      categoryTitle === 'ADJUSTED EBITDA MARGIN' ||
      categoryTitle === 'RETURN ON REVENUE MARGIN');

  if (isMarginCategory) {
    return {
      actualYTD: formatPercentage(totals.actualYTD, 2),
      budgetYTD: formatPercentage(totals.budgetYTD, 2),
      dollarVariance: formatPercentage(totals.dollarVariance, 2),
      percentVariance: totals.percentVariance,
      forecastFY: formatPercentage(totals.forecastFY, 2),
      budgetFY: formatPercentage(totals.budgetFY, 2),
      dollarVarianceFY: formatPercentage(totals.dollarVarianceFY, 2),
      percentVarianceFY: totals.percentVarianceFY,
    };
  }

  return {
    actualYTD: totals.actualYTD,
    budgetYTD: totals.budgetYTD,
    dollarVariance: totals.dollarVariance,
    percentVariance: totals.percentVariance,
    forecastFY: totals.forecastFY,
    budgetFY: totals.budgetFY,
    dollarVarianceFY: totals.dollarVarianceFY,
    percentVarianceFY: totals.percentVarianceFY,
  };
};

export const convertPTDTotalForReportTable = (
  totals: PTDCategoryTotals,
  categoryTitle?: string
): Record<string, number | string> => {
  const isMarginCategory =
    categoryTitle &&
    (categoryTitle === 'EBITDA MARGIN' ||
      categoryTitle === 'ADJUSTED EBITDA MARGIN' ||
      categoryTitle === 'RETURN ON REVENUE MARGIN');

  if (isMarginCategory) {
    return {
      ptdActual: formatPercentage(totals.ptdActual, 2),
      ptdBudget: formatPercentage(totals.ptdBudget, 2),
      ptdVariance: formatPercentage(totals.ptdVariance, 2),
      ptdVariancePercent: totals.ptdVariancePercent,
      ytdActual: formatPercentage(totals.ytdActual, 2),
      ytdBudget: formatPercentage(totals.ytdBudget, 2),
      ytdVariance: formatPercentage(totals.ytdVariance, 2),
      ytdVariancePercent: totals.ytdVariancePercent,
    };
  }

  return {
    ptdActual: totals.ptdActual,
    ptdBudget: totals.ptdBudget,
    ptdVariance: totals.ptdVariance,
    ptdVariancePercent: totals.ptdVariancePercent,
    ytdActual: totals.ytdActual,
    ytdBudget: totals.ytdBudget,
    ytdVariance: totals.ytdVariance,
    ytdVariancePercent: totals.ytdVariancePercent,
  };
};

export const convertThousandTotalForReportTable = (
  totals: ThousandCategoryTotals,
  categoryTitle?: string
): Record<string, number | string> => {
  const isMarginCategory =
    categoryTitle &&
    (categoryTitle === 'EBITDA MARGIN' ||
      categoryTitle === 'ADJUSTED EBITDA MARGIN' ||
      categoryTitle === 'RETURN ON REVENUE MARGIN');

  if (isMarginCategory) {
    return {
      actual: formatPercentage(totals.actual, 1),
      budget: formatPercentage(totals.budget, 1),
      dollarVariance: formatPercentage(totals.dollarVariance, 1),
      forecast: formatPercentage(totals.forecast, 1),
      budgetFY: formatPercentage(totals.budgetFY, 1),
      dollarVarianceFY: formatPercentage(totals.dollarVarianceFY, 1),
    };
  }

  return {
    actual: totals.actual,
    budget: totals.budget,
    dollarVariance: totals.dollarVariance,
    percentVariance: totals.budget === 0 ? 0 : (totals.dollarVariance / totals.budget) * 100,
    forecast: totals.forecast,
    budgetFY: totals.budgetFY,
    dollarVarianceFY: totals.dollarVarianceFY,
    percentVarianceFY: totals.budgetFY === 0 ? 0 : (totals.dollarVarianceFY / totals.budgetFY) * 100,
  };
};

export const formatThousandCellValue = (
  value: number | string,
  columnId: string,
  isMarginCategory: boolean,
  isUnitsCategory: boolean,
  total: ThousandCategoryTotals
): string => {
  if (typeof value !== 'number') return value?.toString() ?? '';

  if (columnId.toLowerCase().includes('percent')) {
    return formatPercentage(value);
  }

  if (isMarginCategory) {
    return formatPercentage(value);
  }

  if (columnId === 'percentVariance') {
    if (total.budget === 0) return '0.0%';
    const percentage = (total.dollarVariance / total.budget) * 100;
    return formatPercentage(percentage);
  }

  if (columnId === 'percentVarianceFY') {
    if (total.budgetFY === 0) return '0.0%';
    const percentage = (total.dollarVarianceFY / total.budgetFY) * 100;
    return formatPercentage(percentage);
  }

  if (columnId === 'dollarVariance' || columnId === 'dollarVarianceFY') {
    return formatVariance(value, isUnitsCategory);
  }

  if (isUnitsCategory) {
    return formatNumber(value);
  }

  return formatCurrency(value);
};

export const formatTableCellValue = (
  value: number | string,
  columnId: string
): string => {
  if (typeof value !== 'number') return value?.toString() ?? '';

  if (columnId.toLowerCase().includes('percent')) {
    return formatPercentage(value);
  }

  if (columnId === 'dollarVariance' || columnId === 'dollarVarianceFY' || 
      columnId === 'ptdVariance' || columnId === 'ytdVariance') {
    return formatCurrency(value);
  }

  return formatNumber(value);
};

export const getTableCellStyle = (columnIndex: number): React.CSSProperties => {
  if (columnIndex === 1) {
    return { borderLeft: '2px solid black' };
  }
  if (columnIndex === 4) {
    return { borderRight: '2px solid black' };
  }
  if (columnIndex === 5) {
    return { borderLeft: '2px solid black' };
  }
  if (columnIndex === 8) {
    return { borderRight: '2px solid black' };
  }
  return {};
};

export const getSectionRowStyle = (columnIndex: number): React.CSSProperties => {
  if (columnIndex === 1) {
    return { borderLeft: '2px solid black' };
  }
  if (columnIndex === 4) {
    return { borderRight: '2px solid black' };
  }
  if (columnIndex === 5) {
    return { borderLeft: '2px solid black' };
  }
  if (columnIndex === 8) {
    return { borderRight: '2px solid black' };
  }
  return {};
};

export const getMarginCategoryBackgroundClass = (categoryTitle: string): string => {
  return [
    'EBITDA MARGIN',
    'ADD BACK: NON-RECURRING ITEMS',
    'ADJUSTED EBITDA MARGIN',
  ].includes(categoryTitle)
    ? 'bg-white'
    : 'bg-[#DEEFFF]';
};

export const shouldShowSectionHeader = (categoryTitle: string): boolean => {
  return categoryTitle === 'TOTAL REVENUE' || categoryTitle === 'TOTAL EXPENSES';
};

export const getSectionHeaderText = (categoryTitle: string): string => {
  if (categoryTitle === 'TOTAL REVENUE') return 'REVENUE';
  if (categoryTitle === 'TOTAL EXPENSES') return 'EXPENSES';
  return '';
};