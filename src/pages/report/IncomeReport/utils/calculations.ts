import {
  YTDDataItem,
  PTDDataItem,
  ThousandDataItem,
  BaseCategoryTotals,
  PTDCategoryTotals,
  ThousandCategoryTotals,
  CategoryData,
  PTDCategoryData,
} from '../types/statementTypes';

export const sumByProperty = <T, K extends keyof T>(
  items: T[],
  property: K
): number => {
  return items.reduce((sum, item) => sum + ((item[property] as number) || 0), 0);
};

export const calculatePercentVariance = (variance: number, budget: number): number => {
  if (budget === 0) return 0;
  return (variance / budget) * 100;
};

export const calculateYTDTotals = (data: YTDDataItem[]): BaseCategoryTotals => {
  const actualYTD = sumByProperty(data, 'Actual');
  const budgetYTD = sumByProperty(data, 'budget');
  const dollarVariance = sumByProperty(data, 'variance');
  const percentVariance = calculatePercentVariance(dollarVariance, budgetYTD);

  const forecastFY = sumByProperty(data, 'forecast');
  const budgetFY = sumByProperty(data, 'budget1');
  const dollarVarianceFY = sumByProperty(data, 'Variance1');
  const percentVarianceFY = calculatePercentVariance(dollarVarianceFY, budgetFY);

  return {
    actualYTD,
    budgetYTD,
    dollarVariance,
    percentVariance,
    forecastFY,
    budgetFY,
    dollarVarianceFY,
    percentVarianceFY,
  };
};

export const calculatePTDTotals = (data: PTDDataItem[]): PTDCategoryTotals => {
  const ptdActual = sumByProperty(data, 'PTD_Actual');
  const ptdBudget = sumByProperty(data, 'PTD_Budget');
  const ptdVariance = sumByProperty(data, 'PTD_Variance');
  const ptdVariancePercent = sumByProperty(data, 'PTD_Variance_Perct');

  const ytdActual = sumByProperty(data, 'YTD_Actual');
  const ytdBudget = sumByProperty(data, 'YTD_Budget');
  const ytdVariance = sumByProperty(data, 'YTD_Variance');
  const ytdVariancePercent = sumByProperty(data, 'YTD_Variance_Perct');

  return {
    ptdActual,
    ptdBudget,
    ptdVariance,
    ptdVariancePercent,
    ytdActual,
    ytdBudget,
    ytdVariance,
    ytdVariancePercent,
  };
};

export const calculateThousandTotals = (
  data: ThousandDataItem[],
  categoryName?: string
): ThousandCategoryTotals => {
  const isUnitsCategory = categoryName === 'Units';
  const divisor = isUnitsCategory ? 1 : 1000;

  const actual = sumByProperty(data, 'Actual') / divisor;
  const budget = sumByProperty(data, 'budget') / divisor;
  const dollarVariance = sumByProperty(data, 'variance') / divisor;

  const forecast = sumByProperty(data, 'forecast') / divisor;
  const budgetFY = sumByProperty(data, 'budget1') / divisor;
  const dollarVarianceFY = sumByProperty(data, 'Variance1') / divisor;

  return {
    actual,
    budget,
    dollarVariance,
    forecast,
    budgetFY,
    dollarVarianceFY,
  };
};

export const calculateYTDMarginCategory = (
  numeratorCategory: CategoryData | undefined,
  totalRevenueCategory: CategoryData | undefined,
  marginTitle: string
): CategoryData | null => {
  if (!numeratorCategory || !totalRevenueCategory) return null;

  const numeratorTotal = numeratorCategory.total;
  const revenueTotal = totalRevenueCategory.total;

  const actualYTDMargin =
    revenueTotal.actualYTD !== 0
      ? (numeratorTotal.actualYTD / revenueTotal.actualYTD) * 100
      : 0;

  const budgetYTDMargin =
    revenueTotal.budgetYTD !== 0
      ? (numeratorTotal.budgetYTD / revenueTotal.budgetYTD) * 100
      : 0;

  const forecastFYMargin =
    revenueTotal.forecastFY !== 0
      ? (numeratorTotal.forecastFY / revenueTotal.forecastFY) * 100
      : 0;

  const budgetFYMargin =
    revenueTotal.budgetFY !== 0
      ? (numeratorTotal.budgetFY / revenueTotal.budgetFY) * 100
      : 0;

  const dollarVariance = actualYTDMargin - budgetYTDMargin;
  const dollarVarianceFY = forecastFYMargin - budgetFYMargin;

  return {
    title: marginTitle,
    items: [],
    total: {
      actualYTD: actualYTDMargin,
      budgetYTD: budgetYTDMargin,
      dollarVariance,
      percentVariance:
        budgetYTDMargin !== 0 ? (dollarVariance / budgetYTDMargin) * 100 : 0,
      forecastFY: forecastFYMargin,
      budgetFY: budgetFYMargin,
      dollarVarianceFY,
      percentVarianceFY:
        budgetFYMargin !== 0 ? (dollarVarianceFY / budgetFYMargin) * 100 : 0,
    },
  };
};

export const calculatePTDMarginCategory = (
  numeratorCategory: PTDCategoryData | undefined,
  totalRevenueCategory: PTDCategoryData | undefined,
  marginTitle: string
): PTDCategoryData | null => {
  if (!numeratorCategory || !totalRevenueCategory) return null;

  const numeratorTotal = numeratorCategory.total;
  const revenueTotal = totalRevenueCategory.total;

  const ptdActualMargin =
    revenueTotal.ptdActual !== 0
      ? (numeratorTotal.ptdActual / revenueTotal.ptdActual) * 100
      : 0;

  const ptdBudgetMargin =
    revenueTotal.ptdBudget !== 0
      ? (numeratorTotal.ptdBudget / revenueTotal.ptdBudget) * 100
      : 0;

  const ytdActualMargin =
    revenueTotal.ytdActual !== 0
      ? (numeratorTotal.ytdActual / revenueTotal.ytdActual) * 100
      : 0;

  const ytdBudgetMargin =
    revenueTotal.ytdBudget !== 0
      ? (numeratorTotal.ytdBudget / revenueTotal.ytdBudget) * 100
      : 0;

  const ptdVariance = ptdActualMargin - ptdBudgetMargin;
  const ytdVariance = ytdActualMargin - ytdBudgetMargin;

  return {
    title: marginTitle,
    items: [],
    total: {
      ptdActual: ptdActualMargin,
      ptdBudget: ptdBudgetMargin,
      ptdVariance,
      ptdVariancePercent:
        ptdBudgetMargin !== 0 ? (ptdVariance / ptdBudgetMargin) * 100 : 0,
      ytdActual: ytdActualMargin,
      ytdBudget: ytdBudgetMargin,
      ytdVariance,
      ytdVariancePercent:
        ytdBudgetMargin !== 0 ? (ytdVariance / ytdBudgetMargin) * 100 : 0,
    },
  };
};

export const calculateThousandMargin = (
  numerator: ThousandCategoryTotals,
  denominator: ThousandCategoryTotals
): ThousandCategoryTotals => {
  const calculatePercentage = (num: number, den: number): number => {
    return den !== 0 ? (num / den) * 100 : 0;
  };

  const actualMargin = calculatePercentage(numerator.actual, denominator.actual);
  const budgetMargin = calculatePercentage(numerator.budget, denominator.budget);
  const forecastMargin = calculatePercentage(numerator.forecast, denominator.forecast);
  const budgetFYMargin = calculatePercentage(numerator.budgetFY, denominator.budgetFY);

  const dollarVariance = actualMargin - budgetMargin;
  const dollarVarianceFY = forecastMargin - budgetFYMargin;

  return {
    actual: actualMargin,
    budget: budgetMargin,
    dollarVariance,
    forecast: forecastMargin,
    budgetFY: budgetFYMargin,
    dollarVarianceFY,
  };
};