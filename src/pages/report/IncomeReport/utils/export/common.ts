import { Cell, Worksheet } from 'exceljs';

export const formatCurrency = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(absValue);

  return value < 0 ? `(${formatted})` : formatted;
};

export const formatCurrencyWithParentheses = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(absValue);

  return value < 0 ? `(${formatted})` : formatted;
};

export const formatPercentage = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toFixed(1);
  return value < 0 ? `(${formatted}%)` : `${formatted}%`;
};

export const formatPTDCurrencyForPDF = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(absValue);

  return value < 0 ? `(${formatted})` : formatted;
};

export const formatPTDPercentageForPDF = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toFixed(1);
  return value < 0 ? `(${formatted}%)` : `${formatted}%`;
};

export const formatPTDCurrencyForExcel = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(absValue);

  return value < 0 ? `(${formatted})` : formatted;
};

export const formatPTDMarginForExcel = (value: number): string => {
  const absValue = Math.abs(value);
  const formatted = absValue.toFixed(2);
  return value < 0 ? `(${formatted}%)` : `${formatted}%`;
};

export const getConsolidationHeader = (filters?: {
  month: string[];
  year: string;
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}): string => {
  if (!filters) return 'Master Consolidation';

  if (filters.adminBU && filters.adminBU.length > 0) {
    const values = filters.adminBU.join(', ');
    return `Filtered by Admin BU: ${values}`;
  }

  if (filters.marketLeader && filters.marketLeader.length > 0) {
    const values = filters.marketLeader.join(', ');
    return `Filtered by Market Leader: ${values}`;
  }

  if (filters.department && filters.department.length > 0) {
    const values = filters.department.join(', ');
    return `Filtered by Department: ${values}`;
  }

  if (filters.businessType && filters.businessType.length > 0) {
    const values = filters.businessType.join(', ');
    return `Filtered by Business Type: ${values}`;
  }

  return 'Master Consolidation';
};

export const applyExcelBorderStyle = (cell: Cell, columnNumber: number) => {
  const baseBorder = {
    top: { style: 'thin', color: { argb: 'FFDEEFFF' } },
    left: { style: 'thin', color: { argb: 'FFDEEFFF' } },
    bottom: { style: 'thin', color: { argb: 'FFDEEFFF' } },
    right: { style: 'thin', color: { argb: 'FFDEEFFF' } },
  };

  if (columnNumber === 2 || columnNumber === 6) {
    baseBorder.left = { style: 'medium', color: { argb: 'FF000000' } };
  }
  
  if (columnNumber === 5 || columnNumber === 9) {
    baseBorder.right = { style: 'medium', color: { argb: 'FF000000' } };
  }

  cell.border = baseBorder;
};

export const addExcelBottomBorder = (worksheet: Worksheet, columnCount: number) => {
  const bottomBorderRow = worksheet.addRow(Array(columnCount).fill(''));
  bottomBorderRow.eachCell((cell: Cell, colNumber: number) => {
    if (colNumber > 1) {
      cell.border = {
        bottom: { style: 'medium', color: { argb: 'FF000000' } },
      };
    }
  });
  bottomBorderRow.height = 2;
};