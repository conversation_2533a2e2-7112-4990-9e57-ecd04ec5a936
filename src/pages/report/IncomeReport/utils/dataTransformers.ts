import {
  APIRequestBody,
  BaseRevenueItem,
  CategoryData,
  PTDCategoryData,
  PTDDataItem,
  PTDFormattedData,
  PTDRevenueItem,
  StatementFilters,
  StatementFormattedData,
  ThousandCategoryData,
  ThousandDataItem,
  ThousandFormattedData,
  ThousandRevenueItem,
  YTDDataItem,
} from '../types/statementTypes';
import {
  calculatePercentVariance,
  calculatePTDMarginCategory,
  calculatePTDTotals,
  calculateThousandMargin,
  calculateThousandTotals,
  calculateYTDMarginCategory,
  calculateYTDTotals,
  sumByProperty,
} from './calculations';
import { getMonthNumber, sortItemsByDescription } from './sorting';

export const formatYTDStatementData = (
  data: YTDDataItem[],
): StatementFormattedData => {
  if (!data || data.length === 0) {
    return {
      lastMonth: 'January 2025',
      categoryData: [],
    };
  }

  const categories = Array.from(
    new Set(data.map((item) => item.ExpandTotal)),
  ).filter((category) => category !== 'Units');

  const extendedCategories = [...categories];
  if (categories.includes('RETURN ON REVENUE')) {
    extendedCategories.push('RETURN ON REVENUE TOTAL');
  }

  const categoryDataMap = new Map<string, CategoryData>();

  extendedCategories.forEach((category) => {
    const categoryItems = getCategoryItems(data, category);
    const filteredItems = filterCategoryItems(categoryItems, category);
    const groupedItems = groupYTDByCategory(filteredItems, category);
    const totalData = calculateYTDTotals(filteredItems);

    categoryDataMap.set(category, {
      title: category,
      items: groupedItems,
      total: totalData,
    });
  });

  const categoryDataArray = Array.from(categoryDataMap.values());

  addYTDMarginCategories(categoryDataArray, categoryDataMap);

  const lastMonth =
    data.length > 0 ? `${data[0].monthName} ${data[0].year}` : 'January 2025';

  return {
    lastMonth,
    categoryData: categoryDataArray,
  };
};

export const formatPTDStatementData = (
  data: PTDDataItem[],
): PTDFormattedData => {
  if (!data || data.length === 0) {
    return {
      lastMonth: 'January 2025',
      categoryData: [],
    };
  }

  const categories = Array.from(
    new Set(data.map((item) => item.ExpandTotal)),
  ).filter((category) => category !== 'Units');

  const extendedCategories = [...categories];
  if (categories.includes('RETURN ON REVENUE')) {
    extendedCategories.push('RETURN ON REVENUE TOTAL');
  }

  const categoryDataMap = new Map<string, PTDCategoryData>();

  extendedCategories.forEach((category) => {
    const categoryItems = getPTDCategoryItems(data, category);
    const filteredItems = filterCategoryItems(categoryItems, category);
    const groupedItems = groupPTDByCategory(filteredItems, category);
    const totalData = calculatePTDTotals(filteredItems);

    categoryDataMap.set(category, {
      title: category,
      items: groupedItems,
      total: totalData,
    });
  });

  const categoryDataArray = Array.from(categoryDataMap.values());

  addPTDMarginCategories(categoryDataArray, categoryDataMap);

  return {
    lastMonth: 'January 2025',
    categoryData: categoryDataArray,
  };
};

export const formatThousandStatementData = (
  data: ThousandDataItem[],
): ThousandFormattedData => {
  if (!data || data.length === 0) {
    return {
      categoryData: [],
    };
  }

  const categories = Array.from(
    new Set(data.map((item) => item.ExpandTotal)),
  ).filter(
    (category) =>
      category !== 'RETURN ON REVENUE' && category !== 'RETURN ON REVENUE1',
  );

  console.log('categories', categories);

  const categoryDataMap = new Map<string, ThousandCategoryData>();

  categories.forEach((category) => {
    const categoryItems = data.filter(
      (item) => item.ExpandTotal === category && item.Total !== '',
    );

    const filteredItems = filterCategoryItems(categoryItems, category);
    const groupedItems = groupThousandByCategory(filteredItems);
    const totalData = calculateThousandTotals(filteredItems, category);

    categoryDataMap.set(category, {
      title: category,
      items: groupedItems,
      total: totalData,
    });
  });

  const categoryDataArray = Array.from(categoryDataMap.values());

  addThousandMarginCategories(categoryDataArray, categoryDataMap);

  return {
    categoryData: categoryDataArray,
  };
};

const getCategoryItems = (
  data: YTDDataItem[],
  category: string,
): YTDDataItem[] => {
  return category === 'RETURN ON REVENUE'
    ? data.filter(
        (item) => item.ExpandTotal === category && item.Total !== 'TOTAL',
      )
    : category === 'RETURN ON REVENUE TOTAL'
      ? data.filter(
          (item) =>
            item.ExpandTotal === 'RETURN ON REVENUE' && item.Total === 'TOTAL',
        )
      : data.filter(
          (item) => item.ExpandTotal === category && item.Total !== '',
        );
};

const getPTDCategoryItems = (
  data: PTDDataItem[],
  category: string,
): PTDDataItem[] => {
  return category === 'RETURN ON REVENUE'
    ? data.filter(
        (item) => item.ExpandTotal === category && item.Total !== 'TOTAL',
      )
    : category === 'RETURN ON REVENUE TOTAL'
      ? data.filter(
          (item) =>
            item.ExpandTotal === 'RETURN ON REVENUE' && item.Total === 'TOTAL',
        )
      : data.filter(
          (item) => item.ExpandTotal === category && item.Total !== '',
        );
};

const filterCategoryItems = <T extends { Total: string }>(
  items: T[],
  category: string,
): T[] => {
  return category === 'TOTAL REVENUE'
    ? items.filter((item) => item.Total !== 'Acquisition Fees')
    : items;
};

const groupYTDByCategory = (
  data: YTDDataItem[],
  category?: string,
): BaseRevenueItem[] => {
  const categoriesMap: Map<string, YTDDataItem[]> = new Map();

  data.forEach((item) => {
    const groupKey = getGroupKey(item, category);
    if (!categoriesMap.has(groupKey)) {
      categoriesMap.set(groupKey, []);
    }
    categoriesMap.get(groupKey)?.push(item);
  });

  const items = Array.from(categoriesMap.entries()).map(([name, items]) => {
    const actualYTD = sumByProperty(items, 'Actual');
    const budgetYTD = sumByProperty(items, 'budget');
    const dollarVariance = sumByProperty(items, 'variance');
    const percentVariance = calculatePercentVariance(dollarVariance, budgetYTD);

    const forecastFY = sumByProperty(items, 'forecast');
    const budgetFY = sumByProperty(items, 'budget1');
    const dollarVarianceFY = sumByProperty(items, 'Variance1');
    const percentVarianceFY = calculatePercentVariance(
      dollarVarianceFY,
      budgetFY,
    );

    return {
      name,
      actualYTD,
      budgetYTD,
      dollarVariance,
      percentVariance,
      forecastFY,
      budgetFY,
      dollarVarianceFY,
      percentVarianceFY,
    };
  });

  return sortItemsByDescription(items);
};

const groupPTDByCategory = (
  data: PTDDataItem[],
  category?: string,
): PTDRevenueItem[] => {
  const categoriesMap: Map<string, PTDDataItem[]> = new Map();

  data.forEach((item) => {
    const groupKey = getGroupKey(item, category);
    if (!categoriesMap.has(groupKey)) {
      categoriesMap.set(groupKey, []);
    }
    categoriesMap.get(groupKey)?.push(item);
  });

  const items = Array.from(categoriesMap.entries()).map(([name, items]) => {
    const ptdActual = sumByProperty(items, 'PTD_Actual');
    const ptdBudget = sumByProperty(items, 'PTD_Budget');
    const ptdVariance = sumByProperty(items, 'PTD_Variance');
    const ptdVariancePercent = sumByProperty(items, 'PTD_Variance_Perct');

    const ytdActual = sumByProperty(items, 'YTD_Actual');
    const ytdBudget = sumByProperty(items, 'YTD_Budget');
    const ytdVariance = sumByProperty(items, 'YTD_Variance');
    const ytdVariancePercent = sumByProperty(items, 'YTD_Variance_Perct');

    return {
      name,
      ptdActual,
      ptdBudget,
      ptdVariance,
      ptdVariancePercent,
      ytdActual,
      ytdBudget,
      ytdVariance,
      ytdVariancePercent,
    };
  });

  return sortItemsByDescription(items);
};

const groupThousandByCategory = (
  data: ThousandDataItem[],
): ThousandRevenueItem[] => {
  const categoriesMap: Map<string, ThousandDataItem[]> = new Map();

  data.forEach((item) => {
    if (!categoriesMap.has(item.Total)) {
      categoriesMap.set(item.Total, []);
    }
    categoriesMap.get(item.Total)?.push(item);
  });

  const items = Array.from(categoriesMap.entries()).map(([name, items]) => {
    const isUnitsCategory = name === 'Units';
    const divisor = isUnitsCategory ? 1 : 1000;

    const actual = sumByProperty(items, 'Actual') / divisor;
    const budget = sumByProperty(items, 'budget') / divisor;
    const dollarVariance = sumByProperty(items, 'variance') / divisor;

    const forecast = sumByProperty(items, 'forecast') / divisor;
    const budgetFY = sumByProperty(items, 'budget1') / divisor;
    const dollarVarianceFY = sumByProperty(items, 'Variance1') / divisor;

    return {
      name,
      description: name,
      actual,
      budget,
      dollarVariance,
      forecast,
      budgetFY,
      dollarVarianceFY,
    };
  });

  return sortItemsByDescription(items);
};

const getGroupKey = (item: { Total: string }, category?: string): string => {
  return category === 'RETURN ON REVENUE' && item.Total === ''
    ? 'Total'
    : category === 'RETURN ON REVENUE TOTAL'
      ? 'Total'
      : item.Total;
};

const addYTDMarginCategories = (
  categoryDataArray: CategoryData[],
  categoryDataMap: Map<string, CategoryData>,
): void => {
  const totalRevenueCategory = categoryDataMap.get('TOTAL REVENUE');
  const ebitdaCategory = categoryDataMap.get('EBITDA');
  const adjustedEbitdaCategory = categoryDataMap.get('ADJUSTED EBITDA');
  const returnOnRevenueCategory = categoryDataMap.get(
    'RETURN ON REVENUE TOTAL',
  );

  const ebitdaMargin = calculateYTDMarginCategory(
    ebitdaCategory,
    totalRevenueCategory,
    'EBITDA MARGIN',
  );
  if (ebitdaMargin) categoryDataArray.push(ebitdaMargin);

  const adjustedEbitdaMargin = calculateYTDMarginCategory(
    adjustedEbitdaCategory,
    totalRevenueCategory,
    'ADJUSTED EBITDA MARGIN',
  );
  if (adjustedEbitdaMargin) categoryDataArray.push(adjustedEbitdaMargin);

  const returnOnRevenueMargin = calculateYTDMarginCategory(
    returnOnRevenueCategory,
    totalRevenueCategory,
    'RETURN ON REVENUE MARGIN',
  );
  if (returnOnRevenueMargin) categoryDataArray.push(returnOnRevenueMargin);
};

const addPTDMarginCategories = (
  categoryDataArray: PTDCategoryData[],
  categoryDataMap: Map<string, PTDCategoryData>,
): void => {
  const totalRevenueCategory = categoryDataMap.get('TOTAL REVENUE');
  const ebitdaCategory = categoryDataMap.get('EBITDA');
  const adjustedEbitdaCategory = categoryDataMap.get('ADJUSTED EBITDA');
  const returnOnRevenueCategory = categoryDataMap.get(
    'RETURN ON REVENUE TOTAL',
  );

  const ebitdaMargin = calculatePTDMarginCategory(
    ebitdaCategory,
    totalRevenueCategory,
    'EBITDA MARGIN',
  );
  if (ebitdaMargin) categoryDataArray.push(ebitdaMargin);

  const adjustedEbitdaMargin = calculatePTDMarginCategory(
    adjustedEbitdaCategory,
    totalRevenueCategory,
    'ADJUSTED EBITDA MARGIN',
  );
  if (adjustedEbitdaMargin) categoryDataArray.push(adjustedEbitdaMargin);

  const returnOnRevenueMargin = calculatePTDMarginCategory(
    returnOnRevenueCategory,
    totalRevenueCategory,
    'RETURN ON REVENUE MARGIN',
  );
  if (returnOnRevenueMargin) categoryDataArray.push(returnOnRevenueMargin);
};

const addThousandMarginCategories = (
  categoryDataArray: ThousandCategoryData[],
  categoryDataMap: Map<string, ThousandCategoryData>,
): void => {
  const totalRevenue = categoryDataMap.get('TOTAL REVENUE');
  const ebitda = categoryDataMap.get('EBITDA');
  const adjustedEbitda = categoryDataMap.get('ADJUSTED EBITDA');

  if (totalRevenue && ebitda) {
    const ebitdaMargin = calculateThousandMargin(
      ebitda.total,
      totalRevenue.total,
    );
    const ebitdaMarginCategory = {
      title: 'EBITDA MARGIN',
      items: [
        {
          name: 'EBITDA Margin %',
          description: 'EBITDA Margin %',
          ...ebitdaMargin,
        },
      ],
      total: ebitdaMargin,
    };
    categoryDataMap.set('EBITDA MARGIN', ebitdaMarginCategory);
    categoryDataArray.push(ebitdaMarginCategory);
  }

  if (totalRevenue && adjustedEbitda) {
    const adjustedEbitdaMargin = calculateThousandMargin(
      adjustedEbitda.total,
      totalRevenue.total,
    );
    const adjustedEbitdaMarginCategory = {
      title: 'ADJUSTED EBITDA MARGIN',
      items: [
        {
          name: 'Adjusted EBITDA Margin %',
          description: 'Adjusted EBITDA Margin %',
          ...adjustedEbitdaMargin,
        },
      ],
      total: adjustedEbitdaMargin,
    };
    categoryDataMap.set('ADJUSTED EBITDA MARGIN', adjustedEbitdaMarginCategory);
    categoryDataArray.push(adjustedEbitdaMarginCategory);
  }
};

export const createAPIRequestBody = (
  filters: StatementFilters,
): APIRequestBody => {
  const monthNumbers =
    filters.month.length > 0
      ? filters.month.map((month) => getMonthNumber(month))
      : ['1'];

  const yearValue = filters.year || '2025';

  return {
    year: yearValue,
    month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
    department:
      filters.department.length === 0
        ? null
        : filters.department.length === 1
          ? filters.department[0]
          : filters.department,
    businessType:
      filters.businessType.length === 0
        ? null
        : filters.businessType.length === 1
          ? filters.businessType[0]
          : filters.businessType,
    marketleader:
      filters.marketLeader.length === 0
        ? null
        : filters.marketLeader.length === 1
          ? filters.marketLeader[0]
          : filters.marketLeader,
    adminBu:
      filters.adminBU.length === 0
        ? null
        : filters.adminBU.length === 1
          ? filters.adminBU[0]
          : filters.adminBU,
  };
};
