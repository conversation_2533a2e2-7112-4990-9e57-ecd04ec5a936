import { CategoryData, PTDCategoryData, ThousandCategoryData } from '../types/statementTypes';

export const sortOrder = [
  'TOTAL REVENUE',
  'TOTAL EXPENSES',
  'NET INCOME',
  'ADD BACK:',
  'EBITDA',
  'EBITDA MARGIN',
  'ADD BACK: NON-RECURRING ITEMS',
  'ADJUSTED EBITDA',
  'ADJUSTED EBITDA MARGIN',
  'RETURN ON REVENUE',
  'RETURN ON REVENUE TOTAL',
  'RETURN ON REVENUE MARGIN',
  'RETURN ON REVENUE1',
  'Units',
];

export const descriptionSortOrder = [
  'Salaries & Related',
  'Employee Benefits Expenses',
  'Property Management Fees',
  'Other PM Fees',
  'Other Service Revenue',
  'D&C PM Rehab',
  'Developer & Construction Fees',
  'Asset Management Fee',
  'Employee Benefits Revenue',
  'Other Revenue',
  'Advertising & Promotion',
  'Travel & Entertainment',
  'Automotive',
  'Rent & Related',
  'Office/Furniture/G&A/Mail/Phone',
  'Recruiting & Other Employment Related',
  'Resident Credit Verification',
  'Professional Fees',
  'Computer',
  'Insurance',
  'Project Related Expenses',
  'Transition Costs',
  'Other Expenses',
  'Interest/Taxes/Depreciation/Amortization',
];

export const sortCategories = (
  categories: CategoryData[] | PTDCategoryData[] | ThousandCategoryData[]
): CategoryData[] | PTDCategoryData[] | ThousandCategoryData[] => {
  return [...categories].sort((a, b) => {
    const indexA = sortOrder.indexOf(a.title);
    const indexB = sortOrder.indexOf(b.title);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    return 0;
  });
};

export const sortItemsByDescription = <T extends { name: string }>(items: T[]): T[] => {
  return items.sort((a, b) => {
    const indexA = descriptionSortOrder.indexOf(a.name);
    const indexB = descriptionSortOrder.indexOf(b.name);

    if (indexA !== -1 && indexB !== -1) {
      return indexA - indexB;
    }

    if (indexA !== -1) return -1;
    if (indexB !== -1) return 1;

    return a.name.localeCompare(b.name);
  });
};

export const getMonthNumber = (monthName: string): string => {
  const months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };

  return months[monthName as keyof typeof months] || '1';
};

export const getMonthsOrderArray = () => [
  'January',
  'February',
  'March',
  'April',
  'May',
  'June',
  'July',
  'August',
  'September',
  'October',
  'November',
  'December',
];

export const formatPeriodDisplay = (months: string[], year: string): string => {
  if (months.length === 0) return `January ${year}`;
  
  const monthsOrder = getMonthsOrderArray();
  
  if (months.length === 1) {
    return months[0] === 'January' ? `January ${year}` : `${months[0]} ${year}`;
  }
  
  const latestMonth = months.reduce((latest, curr) => {
    return monthsOrder.indexOf(curr) > monthsOrder.indexOf(latest) ? curr : latest;
  }, months[0]);
  
  return `January - ${latestMonth} ${year}`;
};