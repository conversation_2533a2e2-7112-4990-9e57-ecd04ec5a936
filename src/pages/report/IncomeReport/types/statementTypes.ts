import React from 'react';

export interface BaseDataItem {
  ExpandTotal: string;
  Total: string;
}

export interface YTDDataItem extends BaseDataItem {
  PropertyBu: string;
  AdminName: string;
  BusinessType: string;
  Department: string;
  MarketLeader: string | null;
  monthName: string;
  month: number;
  year: number;
  date: string;
  Actual: number;
  budget: number;
  variance: number;
  forecast: number;
  budget1: number;
  Variance1: number;
}

export interface PTDDataItem extends BaseDataItem {
  PTD_Actual: number;
  PTD_Budget: number;
  PTD_Variance: number;
  PTD_Variance_Perct: number;
  YTD_Actual: number;
  YTD_Budget: number;
  YTD_Variance: number;
  YTD_Variance_Perct: number;
}

export interface ThousandDataItem extends BaseDataItem {
  Date: string;
  Actual: number;
  budget: number;
  variance: number;
  Variance_Pert: number;
  forecast: number;
  budget1: number;
  Variance1: number;
  FY_Variance_Pert: number;
}

export interface BaseCategoryTotals {
  actualYTD: number;
  budgetYTD: number;
  dollarVariance: number;
  percentVariance: number;
  forecastFY: number;
  budgetFY: number;
  dollarVarianceFY: number;
  percentVarianceFY: number;
}

export interface PTDCategoryTotals {
  ptdActual: number;
  ptdBudget: number;
  ptdVariance: number;
  ptdVariancePercent: number;
  ytdActual: number;
  ytdBudget: number;
  ytdVariance: number;
  ytdVariancePercent: number;
}

export interface ThousandCategoryTotals {
  actual: number;
  budget: number;
  dollarVariance: number;
  forecast: number;
  budgetFY: number;
  dollarVarianceFY: number;
}

export interface BaseRevenueItem {
  name: string;
  actualYTD: number;
  budgetYTD: number;
  dollarVariance: number;
  percentVariance: number;
  forecastFY: number;
  budgetFY: number;
  dollarVarianceFY: number;
  percentVarianceFY: number;
}

export interface PTDRevenueItem {
  name: string;
  ptdActual: number;
  ptdBudget: number;
  ptdVariance: number;
  ptdVariancePercent: number;
  ytdActual: number;
  ytdBudget: number;
  ytdVariance: number;
  ytdVariancePercent: number;
}

export interface ThousandRevenueItem {
  name: string;
  description: string;
  actual: number;
  budget: number;
  dollarVariance: number;
  forecast: number;
  budgetFY: number;
  dollarVarianceFY: number;
}

export type RevenueItem = BaseRevenueItem;
export type ExpenseItem = BaseRevenueItem;

export interface BaseCategoryData {
  title: string;
  items: BaseRevenueItem[];
  total: BaseCategoryTotals;
}

export interface PTDCategoryData {
  title: string;
  items: PTDRevenueItem[];
  total: PTDCategoryTotals;
}

export interface ThousandCategoryData {
  title: string;
  items: ThousandRevenueItem[];
  total: ThousandCategoryTotals;
}

export type CategoryData = BaseCategoryData;
export type CategoryTotals = BaseCategoryTotals;

export interface StatementFormattedData {
  categoryData: CategoryData[];
  lastMonth: string;
}

export interface PTDFormattedData {
  categoryData: PTDCategoryData[];
  lastMonth: string;
}

export interface ThousandFormattedData {
  categoryData: ThousandCategoryData[];
}

export interface StatementFilters {
  month: string[];
  year: string;
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}

export interface APIRequestBody {
  year: string;
  month: string | string[];
  department: string | string[] | null;
  businessType: string | string[] | null;
  marketleader: string | string[] | null;
  adminBu: string | string[] | null;
}

export interface StatementColumn {
  id: string;
  header: string | React.ReactNode;
  accessor: (item: any) => React.ReactNode;
  className?: string;
}

export interface MarginCalculationParams {
  numeratorCategory: CategoryData | PTDCategoryData | ThousandCategoryData | undefined;
  totalRevenueCategory: CategoryData | PTDCategoryData | ThousandCategoryData | undefined;
  marginTitle: string;
}