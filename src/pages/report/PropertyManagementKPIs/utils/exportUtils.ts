import {
  OccupancyRentsItem,
  OperationalKpiItem,
  PropertyManagementKpiFilters,
  PropertyPerformanceItem,
} from '@/slice/propertyManagementKpiSlice';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';

const generatePeriodBasedFileName = (
  baseFileName: string,
  filters?: PropertyManagementKpiFilters,
): string => {
  let periodPart = '';

  if (filters?.month?.length && filters.year) {
    if (filters.month.length === 1) {
      periodPart = `${filters.month[0]}_${filters.year}`;
    } else {
      const monthsOrder = [
        'January',
        'February',
        'March',
        'April',
        'May',
        'June',
        'July',
        'August',
        'September',
        'October',
        'November',
        'December',
      ];

      const latestMonth = filters.month.reduce((latest, curr) => {
        return monthsOrder.indexOf(curr) > monthsOrder.indexOf(latest)
          ? curr
          : latest;
      }, filters.month[0]);

      periodPart = `January_to_${latestMonth}_${filters.year}`;
    }
  } else {
    periodPart = new Date().toISOString().split('T')[0];
  }

  // Replace spaces and special characters with underscores for filename safety
  periodPart = periodPart.replace(/[^a-zA-Z0-9_]/g, '_');

  return `${baseFileName}_${periodPart}.xlsx`;
};

const createWorksheetWithHeaders = (
  workbook: ExcelJS.Workbook,
  worksheetName: string,
  title: string,
  filters?: PropertyManagementKpiFilters,
) => {
  const worksheet = workbook.addWorksheet(worksheetName);

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = { bold: true, size: 16, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A1:I1');

  const titleRow = worksheet.addRow([title]);
  titleRow.font = { bold: true, size: 14, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A2:I2');

  const periodText = filters?.month?.length
    ? `For the Period: ${filters.month.join(', ')} ${filters.year}`
    : 'For the Period: Current Period';
  const periodRow = worksheet.addRow([periodText]);
  periodRow.font = { bold: true, size: 12, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A3:I3');

  const filtersText = buildFiltersText(filters);
  const filtersRow = worksheet.addRow([filtersText]);
  filtersRow.font = { bold: true, size: 12, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A4:I4');

  worksheet.addRow([]);

  return worksheet;
};

const buildFiltersText = (filters?: PropertyManagementKpiFilters): string => {
  if (!filters) return 'Master Consolidation';

  if (filters.adminBU && filters.adminBU.length > 0) {
    const values = filters.adminBU.join(', ');
    return `Filtered by Admin BU: ${values}`;
  }
  if (filters.marketLeader && filters.marketLeader.length > 0) {
    const values = filters.marketLeader.join(', ');
    return `Filtered by Market Leader: ${values}`;
  }
  if (filters.department && filters.department.length > 0) {
    const values = filters.department.join(', ');
    return `Filtered by Department: ${values}`;
  }
  if (filters.businessType && filters.businessType.length > 0) {
    const values = filters.businessType.join(', ');
    return `Filtered by Business Type: ${values}`;
  }
  return 'Master Consolidation';
};

const getTargetValue = (category: string): string => {
  const targetMapping: Record<string, string> = {
    'Avg Cost/Turn': '$575',
    'J Turner': '75',
    Google: '4',
    'Collections % MTD': '98%',
    'Average Unit Turn Time T90': '10',
    'Units With Repeat Service Tickets Within 30 Days': '<2%',
    '% Tickets >72 Hours to Close T30': '<5%',
  };
  return targetMapping[category] || 'N/A';
};

const applyHeaderStyling = (
  worksheet: ExcelJS.Worksheet,
  row: ExcelJS.Row,
  isGroupedHeader: boolean = false,
) => {
  const purpleColor = { argb: 'FF43298F' };
  const whiteColor = { argb: 'FFFFFFFF' };

  row.eachCell((cell, colNumber) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: isGroupedHeader ? whiteColor : purpleColor,
    };
    cell.font = {
      color: isGroupedHeader ? { argb: 'FF000000' } : whiteColor,
      bold: true,
      size: 11,
    };
    cell.alignment = {
      horizontal: colNumber === 1 ? 'left' : 'center',
      vertical: 'middle',
    };
    cell.border = {
      top: { style: 'thin', color: { argb: 'FF000000' } },
      left: { style: 'thin', color: { argb: 'FF000000' } },
      bottom: { style: 'thin', color: { argb: 'FF000000' } },
      right: { style: 'thin', color: { argb: 'FF000000' } },
    };
  });

  row.height = 35;
};

const applyDataRowStyling = (
  worksheet: ExcelJS.Worksheet,
  row: ExcelJS.Row,
  isAlternate: boolean = false,
) => {
  const lightPurpleColor = { argb: 'FFF4F4FF' };
  const whiteColor = { argb: 'FFFFFFFF' };

  row.eachCell((cell, colNumber) => {
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: isAlternate ? lightPurpleColor : whiteColor,
    };
    cell.alignment = {
      horizontal: colNumber === 1 ? 'left' : 'center',
      vertical: 'middle',
    };
    cell.border = {
      top: { style: 'thin', color: { argb: 'FFDEEFFF' } },
      left: { style: 'thin', color: { argb: 'FFDEEFFF' } },
      bottom: { style: 'thin', color: { argb: 'FFDEEFFF' } },
      right: { style: 'thin', color: { argb: 'FFDEEFFF' } },
    };
  });
};

export const exportOccupancyRentsToExcel = (
  data: OccupancyRentsItem[],
  filters?: PropertyManagementKpiFilters,
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = createWorksheetWithHeaders(
      workbook,
      'Occupancy & Rents',
      'Average Occupancy & Rents - Same Store Average Actuals vs. Budget and Submarket',
      filters,
    );

    worksheet.columns = [
      { width: 25 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
    ];

    const headerRow = worksheet.addRow([
      'Category',
      'Central',
      'East',
      'West',
      'Consolidated',
    ]);

    applyHeaderStyling(worksheet, headerRow);

    ['B6', 'C6', 'D6', 'E6'].forEach((cell) => {
      const cellRef = worksheet.getCell(cell);
      cellRef.border = {
        ...cellRef.border,
        left: { style: 'thick', color: { argb: 'FF000000' } },
      };
    });

    const lastCell = worksheet.getCell('E6');
    lastCell.border = {
      ...lastCell.border,
      right: { style: 'thick', color: { argb: 'FF000000' } },
    };

    data.forEach((item, index) => {
      const dataRow = worksheet.addRow([
        item.category,
        item.central.actualVsBudget,
        item.east.actualVsBudget,
        item.west.actualVsBudget,
        item.consolidated.actualVsMarket || item.consolidated.actualVsSubmarket,
      ]);

      applyDataRowStyling(worksheet, dataRow, index % 2 === 1);

      ['B', 'C', 'D', 'E'].forEach((col) => {
        const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
        cellRef.border = {
          ...cellRef.border,
          left: { style: 'thick', color: { argb: 'FF000000' } },
        };
      });

      const lastDataCell = worksheet.getCell(`E${dataRow.number}`);
      lastDataCell.border = {
        ...lastDataCell.border,
        right: { style: 'thick', color: { argb: 'FF000000' } },
      };

      if (index === data.length - 1) {
        ['B', 'C', 'D', 'E'].forEach((col) => {
          const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
          cellRef.border = {
            ...cellRef.border,
            bottom: { style: 'thick', color: { argb: 'FF000000' } },
          };
        });
      }
    });

    const fileName = generatePeriodBasedFileName('Occupancy_Rents', filters);

    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      toast.success('Excel file downloaded successfully');
    });
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export to Excel');
  }
};

export const exportOperationalKPIsToExcel = (
  data: OperationalKpiItem[],
  filters?: PropertyManagementKpiFilters,
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = createWorksheetWithHeaders(
      workbook,
      'Operational KPIs',
      'Operational KPIs - Same Store Average Actuals vs. Target',
      filters,
    );

    worksheet.columns = [
      { width: 25 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
    ];

    const groupedHeaderRow = worksheet.addRow([
      '',
      'Central',
      '',
      'East',
      '',
      'West',
      '',
      'Consolidated',
      '',
    ]);

    applyHeaderStyling(worksheet, groupedHeaderRow, true);
    worksheet.mergeCells('B6:C6');
    worksheet.mergeCells('D6:E6');
    worksheet.mergeCells('F6:G6');
    worksheet.mergeCells('H6:I6');

    ['B6', 'C6', 'D6', 'E6', 'F6', 'G6', 'H6', 'I6'].forEach((cell) => {
      const cellRef = worksheet.getCell(cell);
      cellRef.border = {
        ...cellRef.border,
        left:
          cell === 'B6' || cell === 'D6' || cell === 'F6' || cell === 'H6'
            ? { style: 'thick', color: { argb: 'FF000000' } }
            : { style: 'thin', color: { argb: 'FF000000' } },
        right:
          cell === 'C6' || cell === 'E6' || cell === 'G6' || cell === 'I6'
            ? { style: 'thick', color: { argb: 'FF000000' } }
            : { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thick', color: { argb: 'FF000000' } },
      };
    });

    const headerRow = worksheet.addRow([
      'Category',
      'Actual',
      'Target',
      'Actual',
      'Target',
      'Actual',
      'Target',
      'Actual',
      'Target',
    ]);

    applyHeaderStyling(worksheet, headerRow);

    ['B7', 'D7', 'F7', 'H7'].forEach((cell) => {
      const cellRef = worksheet.getCell(cell);
      cellRef.border = {
        ...cellRef.border,
        left: { style: 'thick', color: { argb: 'FF000000' } },
      };
    });

    ['C7', 'E7', 'G7', 'I7'].forEach((cell) => {
      const cellRef = worksheet.getCell(cell);
      cellRef.border = {
        ...cellRef.border,
        right: { style: 'thick', color: { argb: 'FF000000' } },
      };
    });

    const sortedData = [...data].sort((a, b) =>
      a.category.localeCompare(b.category),
    );

    sortedData.forEach((item, index) => {
      const dataRow = worksheet.addRow([
        item.category,
        item.central.actual,
        getTargetValue(item.category),
        item.east.actual,
        getTargetValue(item.category),
        item.west.actual,
        getTargetValue(item.category),
        item.consolidated.actual,
        getTargetValue(item.category),
      ]);

      applyDataRowStyling(worksheet, dataRow, index % 2 === 1);

      ['B', 'D', 'F', 'H'].forEach((col) => {
        const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
        cellRef.border = {
          ...cellRef.border,
          left: { style: 'thick', color: { argb: 'FF000000' } },
        };
      });

      ['C', 'E', 'G', 'I'].forEach((col) => {
        const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
        cellRef.border = {
          ...cellRef.border,
          right: { style: 'thick', color: { argb: 'FF000000' } },
        };
      });

      if (index === sortedData.length - 1) {
        ['B', 'C', 'D', 'E', 'F', 'G', 'H', 'I'].forEach((col) => {
          const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
          cellRef.border = {
            ...cellRef.border,
            bottom: { style: 'thick', color: { argb: 'FF000000' } },
          };
        });
      }
    });

    const fileName = generatePeriodBasedFileName('Operational_KPIs', filters);

    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      toast.success('Excel file downloaded successfully');
    });
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export to Excel');
  }
};

export const exportPropertyPerformanceToExcel = (
  data: PropertyPerformanceItem[],
  filters?: PropertyManagementKpiFilters,
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = createWorksheetWithHeaders(
      workbook,
      'Property Performance',
      'Property-Level Avg. Performance vs. Budget (All Properties)',
      filters,
    );

    worksheet.columns = [
      { width: 30 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
      { width: 18 },
    ];

    const headerRow = worksheet.addRow([
      'P&L Line Item',
      'Central',
      'East',
      'West',
      'Consolidated',
    ]);

    applyHeaderStyling(worksheet, headerRow);

    ['B6', 'C6', 'D6', 'E6'].forEach((cell) => {
      const cellRef = worksheet.getCell(cell);
      cellRef.border = {
        ...cellRef.border,
        left: { style: 'thick', color: { argb: 'FF000000' } },
      };
    });

    const lastCell = worksheet.getCell('E6');
    lastCell.border = {
      ...lastCell.border,
      right: { style: 'thick', color: { argb: 'FF000000' } },
    };

    const sortedData = [...data].sort((a, b) =>
      a.lineItem.localeCompare(b.lineItem),
    );

    sortedData.forEach((item, index) => {
      const dataRow = worksheet.addRow([
        item.lineItem,
        item.central,
        item.east,
        item.west,
        item.consolidated,
      ]);

      applyDataRowStyling(worksheet, dataRow, index % 2 === 1);

      ['B', 'C', 'D', 'E'].forEach((col) => {
        const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
        cellRef.border = {
          ...cellRef.border,
          left: { style: 'thick', color: { argb: 'FF000000' } },
        };
      });

      const lastDataCell = worksheet.getCell(`E${dataRow.number}`);
      lastDataCell.border = {
        ...lastDataCell.border,
        right: { style: 'thick', color: { argb: 'FF000000' } },
      };

      if (index === sortedData.length - 1) {
        ['B', 'C', 'D', 'E'].forEach((col) => {
          const cellRef = worksheet.getCell(`${col}${dataRow.number}`);
          cellRef.border = {
            ...cellRef.border,
            bottom: { style: 'thick', color: { argb: 'FF000000' } },
          };
        });
      }
    });

    const fileName = generatePeriodBasedFileName(
      'Property_Performance',
      filters,
    );

    workbook.xlsx.writeBuffer().then((buffer) => {
      const blob = new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      a.click();
      window.URL.revokeObjectURL(url);
      toast.success('Excel file downloaded successfully');
    });
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export to Excel');
  }
};
