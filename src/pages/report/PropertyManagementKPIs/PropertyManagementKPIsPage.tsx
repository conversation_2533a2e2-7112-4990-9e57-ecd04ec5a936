import React, { useCallback, useState } from 'react';
import { setActiveTab } from '@/slice/propertyManagementKpiSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Container } from '@/components/common/container';
import PropertyPackageReportFilters from '@/pages/report/components/PropertyPackageReportFilters';
import OccupancyRentsTable from './components/OccupancyRentsTable';
import OperationalKPIsTable from './components/OperationalKPIsTable';
import PropertyPerformanceTable from './components/PropertyPerformanceTable';
import { usePropertyManagementKpiData } from './hooks/usePropertyManagementKpiData';
import {
  exportOccupancyRentsToExcel,
  exportOperationalKPIsToExcel,
  exportPropertyPerformanceToExcel,
} from './utils/exportUtils';

const PropertyManagementKPIsPage: React.FC = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.incomeReport);
  const {
    activeTab,
    sectionLoading,
    sectionErrors,
    occupancyRentsData,
    operationalKpisData,
    propertyPerformanceData,
  } = useSelector((state: RootState) => state.propertyManagementKpi);

  const [ exportType, setExportType ] = useState<'occupancyRents' | 'operationalKpis' | 'propertyPerformance'>('occupancyRents');

  usePropertyManagementKpiData();

  const handleTabChange = (value: string) => {
    dispatch(
      setActiveTab(
        value as 'occupancyRents' | 'operationalKpis' | 'propertyPerformance',
      ),
    );
  };

  const TabsContentUi = ({
    loading,
    error,
    component,
  }: {
    loading?: boolean;
    error?: string | null;
    component: React.ReactNode;
  }) => {
    return (
      <div className="relative">
        {loading && (
          <div className="absolute inset-0 bg-white/80 z-10 flex items-center justify-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#43298F]"></div>
          </div>
        )}
        {error ? (
          <div className="text-red-500 text-center py-8">{error}</div>
        ) : (
          <div className={loading ? 'opacity-50' : ''}>{component}</div>
        )}
      </div>
    );
  };

  const handleExportToExcel = useCallback(() => {
    switch (exportType) {
      case 'occupancyRents': {
        exportOccupancyRentsToExcel(occupancyRentsData, filters);
        break;
      }
      case 'operationalKpis': {
        const sortedOperationalKpisData = [...operationalKpisData].sort((a, b) =>
          a.category.localeCompare(b.category),
        );
        exportOperationalKPIsToExcel(sortedOperationalKpisData, filters);
        break;
      }
      case 'propertyPerformance': {
        exportPropertyPerformanceToExcel(propertyPerformanceData, filters);
        break;
      }
      default: {
        break;
      }
    }
  }, [
    exportType,
    filters,
    occupancyRentsData,
    operationalKpisData,
    propertyPerformanceData,
  ]);

  return (
    <Container title="Property Management KPIs" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <PropertyPackageReportFilters
          businessFilter={false}
          handleExportToExcel={handleExportToExcel}
        />
        </div>

        <div className="min-w-[1024px]">
          <Tabs value={activeTab} onValueChange={handleTabChange}>
            <TabsList className="grid w-full grid-cols-3 mb-6 bg-white border border-[#43298F]">
              <TabsTrigger
                value="occupancyRents"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
                onClick={() => setExportType('occupancyRents')}
              >
                Occupancy & Rents
              </TabsTrigger>
              <TabsTrigger
                value="operationalKpis"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
                onClick={() => setExportType('operationalKpis')}
              >
                Operational KPIs
              </TabsTrigger>
              <TabsTrigger
                value="propertyPerformance"
                className="data-[state=active]:bg-[#43298F] data-[state=active]:text-white hover:bg-[#DEEFFF] transition-colors"
                onClick={() => setExportType('propertyPerformance')}
              >
                Property Performance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="occupancyRents">
              <TabsContentUi
                loading={sectionLoading.occupancyRents}
                error={sectionErrors.occupancyRents}
                component={<OccupancyRentsTable />}
              />
            </TabsContent>

            <TabsContent value="operationalKpis">
              <TabsContentUi
                loading={sectionLoading.operationalKpis}
                error={sectionErrors.operationalKpis}
                component={<OperationalKPIsTable />}
              />
            </TabsContent>

            <TabsContent value="propertyPerformance">
              <TabsContentUi
                loading={sectionLoading.propertyPerformance}
                error={sectionErrors.propertyPerformance}
                component={<PropertyPerformanceTable />}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </Container>
  );
};

export default PropertyManagementKPIsPage;
