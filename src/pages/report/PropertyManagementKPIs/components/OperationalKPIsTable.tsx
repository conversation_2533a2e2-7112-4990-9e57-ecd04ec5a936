import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { formatPeriodDisplay } from '../../IncomeReport/utils/sorting';

const OperationalKPIsTable: React.FC = () => {
  const { operationalKpisData } = useSelector(
    (state: RootState) => state.propertyManagementKpi,
  );
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const sortedOperationalKpisData = [...operationalKpisData].sort((a, b) =>
    a.category.localeCompare(b.category),
  );

  const getTargetValue = (category: string): string => {
    const targetMapping: Record<string, string> = {
      'Avg Cost/Turn': '$575',
      'J Turner': '75',
      'Google': '4',
      'Collections % MTD': '98%',
      'Average Unit Turn Time T90': '10',
      'Units With Repeat Service Tickets Within 30 Days': '<2%',
      '% Tickets >72 Hours to Close T30': '<5%',
    };
    return targetMapping[category] || 'N/A';
  };

  return (
    <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {formatPeriodDisplay(filters.month, filters.year)}
        </h3>
      </div>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Operational KPIs - Same Store Average Actuals vs. Target
        </h3>
      </div>

      {sortedOperationalKpisData.length === 0 ? (
        <div className="text-center p-8 text-gray-500">
          No data available for the selected filters
        </div>
      ) : (
        <div className="border border-[#F3F4FF] overflow-hidden">
          <table className="min-w-full border-collapse">
            <thead>
              {/* Grouped Headers Row */}
              <tr className="text-white">
                <th className="border-[#F3F4FF]">
                  {/* Empty cell for Category column */}
                </th>
                <th
                  colSpan={2}
                  className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                  style={{
                    borderLeft: '2px solid black',
                    borderRight: '2px solid black',
                    borderBottom: '2px solid black',
                  }}
                >
                  Central
                </th>
                <th
                  colSpan={2}
                  className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                  style={{
                    borderLeft: '2px solid black',
                    borderRight: '2px solid black',
                    borderBottom: '2px solid black',
                  }}
                >
                  East
                </th>
                <th
                  colSpan={2}
                  className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                  style={{
                    borderLeft: '2px solid black',
                    borderRight: '2px solid black',
                    borderBottom: '2px solid black',
                  }}
                >
                  West
                </th>
                <th
                  colSpan={2}
                  className="p-3 text-sm font-medium uppercase tracking-wider border border-black text-center bg-[#ffffff] text-black font-bold"
                  style={{
                    borderLeft: '2px solid black',
                    borderRight: '2px solid black',
                    borderBottom: '2px solid black',
                  }}
                >
                  Consolidated
                </th>
              </tr>
              {/* Column Headers Row */}
              <tr className="bg-[#43298F] text-white">
                <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                  Category
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Actual
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderRight: '2px solid black' }}
                >
                  Target
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Actual
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderRight: '2px solid black' }}
                >
                  Target
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Actual
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderRight: '2px solid black' }}
                >
                  Target
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Actual
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderRight: '2px solid black' }}
                >
                  Target
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {sortedOperationalKpisData.map((item, index) => (
                <tr
                  key={index}
                  className={`border-b border-[#DEEFFF] ${
                    index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                  }`}
                >
                  <td className="px-2 py-0 whitespace-nowrap text-left font-medium border-r border-[#DEEFFF]">
                    {item.category}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.central.actual}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderRight: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {getTargetValue(item.category)}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.east.actual}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderRight: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {getTargetValue(item.category)}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.west.actual}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderRight: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {getTargetValue(item.category)}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.consolidated.actual}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center"
                    style={{
                      borderRight: '2px solid black',
                      ...(index === sortedOperationalKpisData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {getTargetValue(item.category)}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default OperationalKPIsTable;
