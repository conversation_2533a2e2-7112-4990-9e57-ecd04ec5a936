import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { formatPeriodDisplay } from '../../IncomeReport/utils/sorting';

const PropertyPerformanceTable: React.FC = () => {
  const { propertyPerformanceData } = useSelector(
    (state: RootState) => state.propertyManagementKpi,
  );
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const sortedPropertyPerformanceData = [...propertyPerformanceData].sort((a, b) =>
    a.lineItem.localeCompare(b.lineItem),
  );

  return (
    <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {formatPeriodDisplay(filters.month, filters.year)}
        </h3>
      </div>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Property-Level Avg. Performance vs. Budget (All Properties)
        </h3>
      </div>

      {sortedPropertyPerformanceData.length === 0 ? (
        <div className="text-center p-8 text-gray-500">
          No data available for the selected filters
        </div>
      ) : (
        <div className="border border-[#F3F4FF] overflow-hidden">
          <table className="min-w-full border-collapse">
            <thead>
              <tr className="bg-[#43298F] text-white">
                <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                  P&L Line Item
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Central
                </th>
                <th 
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  East
                </th>
                <th 
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  West
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black', borderRight: '2px solid black' }}
                >
                  Consolidated
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {sortedPropertyPerformanceData.map((item, index) => (
                <tr
                  key={index}
                  className={`border-b border-[#DEEFFF] ${index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                    }`}
                >
                  <td className="px-2 py-0 whitespace-nowrap text-left font-medium border-r border-[#DEEFFF]">
                    {item.lineItem}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedPropertyPerformanceData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.central}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedPropertyPerformanceData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.east}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === sortedPropertyPerformanceData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.west}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center"
                    style={{
                      borderLeft: '2px solid black',
                      borderRight: '2px solid black',
                      ...(index === sortedPropertyPerformanceData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.consolidated}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default PropertyPerformanceTable;
