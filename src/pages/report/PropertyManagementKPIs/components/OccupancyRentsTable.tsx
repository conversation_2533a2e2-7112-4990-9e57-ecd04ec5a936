import React from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { formatPeriodDisplay } from '../../IncomeReport/utils/sorting';

const OccupancyRentsTable: React.FC = () => {
  const { occupancyRentsData } = useSelector(
    (state: RootState) => state.propertyManagementKpi,
  );
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  return (
    <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg p-4">
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {formatPeriodDisplay(filters.month, filters.year)}
        </h3>
      </div>
      
      <div className="mb-4">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Average Occupancy & Rents - Same Store Average Actuals vs. Budget and
          Submarket
        </h3>
      </div>

      {occupancyRentsData.length === 0 ? (
        <div className="text-center p-8 text-gray-500">
          No data available for the selected filters
        </div>
      ) : (
        <div className="border border-[#F3F4FF] overflow-hidden">
          <table className="min-w-full border-collapse">
            <thead>
              {/* Single Header Row */}
              <tr className="bg-[#43298F] text-white">
                <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                  Category
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  Central
                </th>
                <th 
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  East
                </th>
                <th 
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black' }}
                >
                  West
                </th>
                <th
                  className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center"
                  style={{ borderLeft: '2px solid black', borderRight: '2px solid black' }}
                >
                  Consolidated
                </th>
              </tr>
            </thead>
            <tbody className="bg-white">
              {occupancyRentsData.map((item, index) => (
                <tr
                  key={index}
                  className={`border-b border-[#DEEFFF] ${index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                    }`}
                >
                  <td className="px-2 py-0 whitespace-nowrap text-left font-medium border-r border-[#DEEFFF]">
                    {item.category}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === occupancyRentsData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.central.actualVsBudget}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === occupancyRentsData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.east.actualVsBudget}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center border-r border-[#DEEFFF]"
                    style={{
                      borderLeft: '2px solid black',
                      ...(index === occupancyRentsData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.west.actualVsBudget}
                  </td>
                  <td
                    className="px-2 py-0 whitespace-nowrap text-center"
                    style={{
                      borderLeft: '2px solid black',
                      borderRight: '2px solid black',
                      ...(index === occupancyRentsData.length - 1
                        ? { borderBottom: '2px solid black' }
                        : {}),
                    }}
                  >
                    {item.consolidated.actualVsMarket ||
                      item.consolidated.actualVsSubmarket}
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
};

export default OccupancyRentsTable;
