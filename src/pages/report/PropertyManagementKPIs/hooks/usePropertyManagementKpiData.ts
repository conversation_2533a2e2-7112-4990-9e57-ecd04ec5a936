import { useCallback, useEffect } from 'react';
import {
  getPropertyManagementJTurner,
  getPropertyManagementNOI,
  getPropertyManagementYoY,
  PropertyManagementKpiPayload,
  PropertyManagementKpiResponse,
} from '@/api/propertyManagementKpiApi';
import {
  OccupancyRentsItem,
  OperationalKpiItem,
  PropertyPerformanceItem,
  setOccupancyRentsData,
  setOperationalKpisData,
  setPropertyPerformanceData,
  setSectionError,
  setSectionLoading,
} from '@/slice/propertyManagementKpiSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';
import { formatNumber, formatPercentage } from '@/lib/helpers';

const getMonthNumber = (monthName: string): string => {
  const months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };
  return months[monthName as keyof typeof months] || '1';
};

const transformOccupancyRentsData = (
  apiData: PropertyManagementKpiResponse[],
): OccupancyRentsItem[] => {
  return apiData.map((item) => ({
    category: item.parameter,
    central: {
      actualVsBudget: formatPercentage(item.Central),
      actualVsSubmarket: formatPercentage(item.Central),
    },
    east: {
      actualVsBudget: formatPercentage(item.East),
      actualVsSubmarket: formatPercentage(item.East),
    },
    west: {
      actualVsBudget: formatPercentage(item.West),
      actualVsSubmarket: formatPercentage(item.West),
    },
    consolidated: {
      actualVsBudget: formatPercentage(item.Consolidated),
      actualVsMarket: formatPercentage(item.Consolidated),
    },
  }));
};

const transformOperationalKpisData = (
  apiData: PropertyManagementKpiResponse[],
): OperationalKpiItem[] => {
  return apiData.map((item) => ({
    category: item.parameter,
    central: {
      actual: formatNumber(item.Central),
      target: formatNumber(item.Central * 0.95),
    },
    east: {
      actual: formatNumber(item.East),
      target: formatNumber(item.East * 0.95),
    },
    west: {
      actual: formatNumber(item.West),
      target: formatNumber(item.West * 0.95),
    },
    consolidated: {
      actual: formatNumber(item.Consolidated),
      target: formatNumber(item.Consolidated * 0.95),
    },
  }));
};

const transformPropertyPerformanceData = (
  apiData: PropertyManagementKpiResponse[],
): PropertyPerformanceItem[] => {
  return apiData.map((item) => ({
    lineItem: item.parameter,
    central: formatPercentage(item.Central),
    east: formatPercentage(item.East),
    west: formatPercentage(item.West),
    consolidated: formatPercentage(item.Consolidated),
  }));
};

export const usePropertyManagementKpiData = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const createRequestPayload = (): PropertyManagementKpiPayload => {
    const monthNumbers =
      filters.month.length > 0
        ? filters.month.map((month) => getMonthNumber(month))
        : ['1'];

    return {
      year: filters.year || '2025',
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      department:
        filters.department.length === 0
          ? null
          : filters.department.length === 1
            ? filters.department[0]
            : filters.department,
      businessType:
        filters.businessType.length === 0
          ? null
          : filters.businessType.length === 1
            ? filters.businessType[0]
            : filters.businessType,
      marketleader:
        filters.marketLeader.length === 0
          ? null
          : filters.marketLeader.length === 1
            ? filters.marketLeader[0]
            : filters.marketLeader,
      adminBu:
        filters.adminBU.length === 0
          ? null
          : filters.adminBU.length === 1
            ? filters.adminBU[0]
            : filters.adminBU,
    };
  };

  const fetchOccupancyRentsData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ occupancyRents: true }));
    dispatch(setSectionError({ occupancyRents: null }));

    try {
      const response = await getPropertyManagementYoY(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No occupancy & rents data found for the selected filters',
          );
        }
        const transformedData = transformOccupancyRentsData(response.data);
        dispatch(setOccupancyRentsData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching occupancy rents data:', error);
      dispatch(
        setSectionError({
          occupancyRents: 'Failed to fetch occupancy & rents data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ occupancyRents: false }));
    }
  };

  const fetchOperationalKpisData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ operationalKpis: true }));
    dispatch(setSectionError({ operationalKpis: null }));

    try {
      const response = await getPropertyManagementJTurner(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No operational KPIs data found for the selected filters',
          );
        }
        const transformedData = transformOperationalKpisData(response.data);
        dispatch(setOperationalKpisData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching operational KPIs data:', error);
      dispatch(
        setSectionError({
          operationalKpis: 'Failed to fetch operational KPIs data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ operationalKpis: false }));
    }
  };

  const fetchPropertyPerformanceData = async (
    payload: PropertyManagementKpiPayload,
  ) => {
    dispatch(setSectionLoading({ propertyPerformance: true }));
    dispatch(setSectionError({ propertyPerformance: null }));

    try {
      const response = await getPropertyManagementNOI(payload);
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No property performance data found for the selected filters',
          );
        }
        const transformedData = transformPropertyPerformanceData(response.data);
        dispatch(setPropertyPerformanceData(transformedData));
      }
    } catch (error) {
      console.error('Error fetching property performance data:', error);
      dispatch(
        setSectionError({
          propertyPerformance: 'Failed to fetch property performance data',
        }),
      );
    } finally {
      dispatch(setSectionLoading({ propertyPerformance: false }));
    }
  };

  const fetchAllData = useCallback(async () => {
    const payload = createRequestPayload();

    // Fetch all data in parallel for better performance
    await Promise.all([
      fetchOccupancyRentsData(payload),
      fetchOperationalKpisData(payload),
      fetchPropertyPerformanceData(payload),
    ]);
  }, [filters]);

  useEffect(() => {
    fetchAllData();
  }, [filters, dispatch, fetchAllData]);

  return {
    fetchAllData,
    fetchOccupancyRentsData: () =>
      fetchOccupancyRentsData(createRequestPayload()),
    fetchOperationalKpisData: () =>
      fetchOperationalKpisData(createRequestPayload()),
    fetchPropertyPerformanceData: () =>
      fetchPropertyPerformanceData(createRequestPayload()),
  };
};
