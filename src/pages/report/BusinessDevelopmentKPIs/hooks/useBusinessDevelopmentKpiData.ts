import { useCallback, useEffect } from 'react';
import {
  getBusinessDevelopmentKpi,
  BusinessDevelopmentKpiPayload,
  BusinessDevelopmentKpiResponse,
} from '@/api/businessDevelopmentKpiApi';
import {
  BusinessDevelopmentItem,
  setBusinessDevelopmentData,
  setError,
  setLoading,
} from '@/slice/businessDevelopmentSlice';
import { RootState } from '@/store';
import { useDispatch, useSelector } from 'react-redux';
import { toast } from 'sonner';

const getMonthNumber = (monthName: string): string => {
  const months = {
    January: '1',
    February: '2',
    March: '3',
    April: '4',
    May: '5',
    June: '6',
    July: '7',
    August: '8',
    September: '9',
    October: '10',
    November: '11',
    December: '12',
  };
  return months[monthName as keyof typeof months] || '1';
};

const transformBusinessDevelopmentData = (
  apiData: BusinessDevelopmentKpiResponse[],
): BusinessDevelopmentItem[] => {
  return apiData.map((item, index) => ({
    id: `bd_${index + 1}`,
    property: item.property,
    type: item.type === 'win' ? 'Win' : 'Loss',
    client: item.client,
    units: item.unit,
    status: item.status,
    manager: item.mgr,
    pmFeePercent: item.pm_fees_percent,
    pmFeePer25: item.pm_fees,
    annualPmFees: item.annual_pm_fees,
  }));
};

export const useBusinessDevelopmentKpiData = () => {
  const dispatch = useDispatch();
  const { filters } = useSelector((state: RootState) => state.incomeReport);

  const createRequestPayload = useCallback((): BusinessDevelopmentKpiPayload => {
    const monthNumbers =
      filters.month.length > 0
        ? filters.month.map((month) => getMonthNumber(month))
        : ['1'];

    return {
      year: filters.year || '2025',
      month: monthNumbers.length === 1 ? monthNumbers[0] : monthNumbers,
      department:
        filters.department.length === 0
          ? null
          : filters.department.length === 1
            ? filters.department[0]
            : filters.department,
      businessType:
        filters.businessType.length === 0
          ? null
          : filters.businessType.length === 1
            ? filters.businessType[0]
            : filters.businessType,
      marketleader:
        filters.marketLeader.length === 0
          ? null
          : filters.marketLeader.length === 1
            ? filters.marketLeader[0]
            : filters.marketLeader,
      adminBu:
        filters.adminBU.length === 0
          ? null
          : filters.adminBU.length === 1
            ? filters.adminBU[0]
            : filters.adminBU,
    };
  }, [filters]);

  const fetchBusinessDevelopmentData = useCallback(async () => {
    dispatch(setLoading(true));
    dispatch(setError(null));

    try {
      const payload = createRequestPayload();
      const response = await getBusinessDevelopmentKpi(payload);
      
      if (response && response.data) {
        if (response.data.length === 0) {
          toast.warning(
            'No business development data found for the selected filters',
          );
        }
        
        const transformedData = transformBusinessDevelopmentData(response.data);
        
        // Generate lastUpdated string based on filters
        const lastUpdated = 
          filters.month.length > 0 && filters.month.length === 1
            ? `${filters.month[0]} ${filters.year}`
            : `January - ${filters.month[filters.month.length - 1] || 'January'} ${filters.year}`;
        
        dispatch(setBusinessDevelopmentData({
          items: transformedData,
          lastUpdated,
        }));
      }
    } catch (error) {
      console.error('Error fetching business development data:', error);
      dispatch(setError('Failed to fetch business development data'));
      toast.error('Failed to fetch business development data');
    } finally {
      dispatch(setLoading(false));
    }
  }, [filters, dispatch, createRequestPayload]);

  const fetchData = useCallback(async () => {
    await fetchBusinessDevelopmentData();
  }, [fetchBusinessDevelopmentData]);

  useEffect(() => {
    fetchData();
  }, [filters, dispatch, fetchData]);

  return {
    fetchData,
    refetch: fetchBusinessDevelopmentData,
  };
};