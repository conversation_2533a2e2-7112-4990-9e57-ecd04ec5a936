import {
  BusinessDevelopmentFilters,
  BusinessDevelopmentItem,
} from '@/slice/businessDevelopmentSlice';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';

const buildFiltersText = (filters?: BusinessDevelopmentFilters): string => {
  if (!filters) return 'Master Consolidation';

  if (filters.adminBU && filters.adminBU.length > 0) {
    const values = filters.adminBU.join(', ');
    return `Filtered by Admin BU: ${values}`;
  }
  if (filters.marketLeader && filters.marketLeader.length > 0) {
    const values = filters.marketLeader.join(', ');
    return `Filtered by Market Leader: ${values}`;
  }
  if (filters.department && filters.department.length > 0) {
    const values = filters.department.join(', ');
    return `Filtered by Department: ${values}`;
  }
  if (filters.businessType && filters.businessType.length > 0) {
    const values = filters.businessType.join(', ');
    return `Filtered by Business Type: ${values}`;
  }
  return 'Master Consolidation';
};

const createWorksheetWithHeaders = (
  workbook: ExcelJS.Workbook,
  worksheetName: string,
  title: string,
  filters?: BusinessDevelopmentFilters,
) => {
  const worksheet = workbook.addWorksheet(worksheetName);

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = { bold: true, size: 16, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A1:I1');

  const titleRow = worksheet.addRow([title]);
  titleRow.font = { bold: true, size: 14, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A2:I2');

  const periodText = filters?.month?.length
    ? `For the Period: ${filters.month.join(', ')} ${filters.year}`
    : 'For the Period: Current Period';
  const periodRow = worksheet.addRow([periodText]);
  periodRow.font = { bold: true, size: 12, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A3:I3');

  const filtersText = buildFiltersText(filters);
  const filtersRow = worksheet.addRow([filtersText]);
  filtersRow.font = { bold: true, size: 12, color: { argb: 'FF43298F' } };
  worksheet.mergeCells('A4:I4');

  worksheet.addRow([]);

  return worksheet;
};

export const exportBusinessDevelopmentToExcel = async (
  data: BusinessDevelopmentItem[],
  _lastUpdated: string,
  filters?: BusinessDevelopmentFilters,
) => {
  try {
    const workbook = new ExcelJS.Workbook();
    const worksheet = createWorksheetWithHeaders(
      workbook,
      'Business Development KPIs',
      `Business Development KPIs - Current Month Wins/Losses`,
      filters,
    );

    worksheet.columns = [
      { width: 25 },
      { width: 15 },
      { width: 20 },
      { width: 12 },
      { width: 20 },
      { width: 20 },
      { width: 12 },
      { width: 15 },
      { width: 18 },
    ];

    const headerRow = worksheet.addRow([
      'Property',
      'Type',
      'Client',
      'Units',
      'Status',
      'Prior/New Mgr.',
      'PM Fee %',
      '25 PM Fees',
      'Annual PM Fees',
    ]);

    headerRow.font = { color: { argb: 'FFFFFFFF' }, bold: true, size: 11 };
    headerRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FF43298F' },
    };
    headerRow.height = 35;

    headerRow.eachCell((cell, colNumber) => {
      cell.alignment = {
        horizontal: colNumber === 1 ? 'left' : 'center',
        vertical: 'middle',
      };
      cell.border = {
        top: { style: 'thin', color: { argb: 'FF000000' } },
        left: { style: 'thin', color: { argb: 'FF000000' } },
        bottom: { style: 'thin', color: { argb: 'FF000000' } },
        right: { style: 'thin', color: { argb: 'FF000000' } },
      };
    });

    data.forEach((item, index) => {
      const row = worksheet.addRow([
        item.property,
        item.type,
        item.client,
        item.units,
        item.status,
        item.manager,
        `${item.pmFeePercent.toFixed(1)}%`,
        item.pmFeePer25,
        item.annualPmFees,
      ]);

      const lightPurpleColor = { argb: 'FFF4F4FF' };
      const whiteColor = { argb: 'FFFFFFFF' };
      const winColor = { argb: 'FFE8F5E8' };
      const lossColor = { argb: 'FFFFE8E8' };

      let backgroundColor = index % 2 === 1 ? lightPurpleColor : whiteColor;

      if (item.type === 'Win') {
        backgroundColor = winColor;
      } else if (item.type === 'Loss') {
        backgroundColor = lossColor;
      }

      row.eachCell((cell, colNumber) => {
        cell.fill = {
          type: 'pattern',
          pattern: 'solid',
          fgColor: backgroundColor,
        };
        cell.alignment = {
          horizontal: colNumber === 1 ? 'left' : 'center',
          vertical: 'middle',
        };
        cell.border = {
          top: { style: 'thin', color: { argb: 'FFDEEFFF' } },
          left: { style: 'thin', color: { argb: 'FFDEEFFF' } },
          bottom: { style: 'thin', color: { argb: 'FFDEEFFF' } },
          right: { style: 'thin', color: { argb: 'FFDEEFFF' } },
        };
      });
    });

    const totalUnits = data.reduce((sum, item) => sum + item.units, 0);
    const totalAnnualPmFees = data.reduce(
      (sum, item) => sum + item.annualPmFees,
      0,
    );

    const totalRow = worksheet.addRow([
      'Total',
      '',
      '',
      totalUnits,
      '',
      '',
      '',
      '',
      totalAnnualPmFees,
    ]);

    totalRow.font = { bold: true, color: { argb: 'FF43298F' } };
    totalRow.fill = {
      type: 'pattern',
      pattern: 'solid',
      fgColor: { argb: 'FFDEEFFF' },
    };

    totalRow.eachCell((cell, colNumber) => {
      cell.alignment = {
        horizontal: colNumber === 1 ? 'left' : 'center',
        vertical: 'middle',
      };
      cell.border = {
        top: { style: 'thin', color: { argb: 'FFC7D2FE' } },
        left: { style: 'thin', color: { argb: 'FFC7D2FE' } },
        bottom: { style: 'thin', color: { argb: 'FFC7D2FE' } },
        right: { style: 'thin', color: { argb: 'FFC7D2FE' } },
      };
    });

    worksheet.getColumn(4).numFmt = '#,##0';
    worksheet.getColumn(8).numFmt = '$#,##0';
    worksheet.getColumn(9).numFmt = '$#,##0';

    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `Business_Development_KPIs_${new Date().toISOString().slice(0, 10)}.xlsx`;
    link.click();
    window.URL.revokeObjectURL(url);

    toast.success('Excel file exported successfully');
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export Excel file');
  }
};
