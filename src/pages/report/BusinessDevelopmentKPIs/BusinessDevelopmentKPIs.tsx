import React from 'react';
import { RootState } from '@/store';
import { FileUp } from 'lucide-react';
import { useSelector } from 'react-redux';
import { Button } from '@/components/ui/button';
import { Container } from '@/components/common/container';
import ReportFilters from '../IncomeReport/components/ReportFilters';
import { exportBusinessDevelopmentToExcel } from './utils/exportUtils';
import { useBusinessDevelopmentKpiData } from './hooks/useBusinessDevelopmentKpiData';

const BusinessDevelopmentKPIs: React.FC = () => {
  const { items, loading, error, lastUpdated, filters } = useSelector(
    (state: RootState) => ({
      items: state.businessDevelopment.items,
      loading: state.businessDevelopment.loading,
      error: state.businessDevelopment.error,
      lastUpdated: state.businessDevelopment.lastUpdated,
      filters: state.incomeReport.filters,
    }),
  );

  useBusinessDevelopmentKpiData();

  const handleExportToExcel = () => {
    exportBusinessDevelopmentToExcel(items, lastUpdated, filters);
  };

  if (loading) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center">
          <div className="animate-spin h-8 w-8 border-4 border-[#43298F] border-t-transparent rounded-full mx-auto mb-4"></div>
          <p>Loading report data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-screen w-full items-center justify-center">
        <div className="text-center text-red-600">
          <p>Error: {error}</p>
        </div>
      </div>
    );
  }

  const totalUnits = items.reduce((sum, item) => sum + item.units, 0);
  const totalAnnualPmFees = items.reduce(
    (sum, item) => sum + item.annualPmFees,
    0,
  );

  return (
    <Container title="Business Development KPIs" width="fluid">
      <div className="bg-white shadow-md rounded-lg p-4">
        <div className="mb-6">
          <ReportFilters />
        </div>

        <div className="overflow-x-auto bg-[#F4F4FF] rounded-lg">
          <div className="min-w-[1024px]">
            <div className="bg-[#F4F4FF] p-4 mb-4 rounded-t-lg flex justify-between items-center">
              <h3 className="text-lg font-semibold mb-2 text-[#43298F]">
                Period:{' '}
                {filters.month.length > 0
                  ? (() => {
                      const monthsOrder = [
                        'January',
                        'February',
                        'March',
                        'April',
                        'May',
                        'June',
                        'July',
                        'August',
                        'September',
                        'October',
                        'November',
                        'December',
                      ];
                      const selectedMonths = filters.month;
                      if (
                        selectedMonths.length === 1 &&
                        selectedMonths[0] === 'January'
                      ) {
                        return `January ${filters.year}`;
                      }
                      const latestMonth = selectedMonths.reduce(
                        (latest, curr) => {
                          return monthsOrder.indexOf(curr) >
                            monthsOrder.indexOf(latest)
                            ? curr
                            : latest;
                        },
                        selectedMonths[0],
                      );
                      return `January - ${latestMonth} ${filters.year}`;
                    })()
                  : 'January 2025'}
              </h3>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
                  onClick={handleExportToExcel}
                  disabled={items.length === 0}
                >
                  <FileUp className="mr-2 h-4 w-4" />
                  Export Excel
                </Button>
              </div>
            </div>

            <div className="p-4">
              {items.length > 0 ? (
                <div className="border border-[#F3F4FF] overflow-hidden">
                  <table className="min-w-full border-collapse">
                    <thead>
                      {/* Section Header */}
                      <tr className="text-black">
                        <td
                          colSpan={9}
                          className="px-3 py-2 font-bold text-left"
                        >
                          Current Month Wins/Losses
                        </td>
                      </tr>
                      {/* Column Headers Row */}
                      <tr className="bg-[#43298F] text-white">
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                          Property
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                          Type
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                          Client
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                          Units
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                          Status
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-left">
                          Prior/New Mgr.
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                          PM Fee %
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                          25 PM Fees
                        </th>
                        <th className="p-3 text-xs font-medium uppercase tracking-wider border border-black text-center">
                          Annual PM Fees
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      {items.map((item, index) => (
                        <tr
                          key={item.id}
                          className={`border-b border-[#DEEFFF] ${
                            index % 2 === 1 ? 'bg-[#F4F4FF]' : 'bg-white'
                          }`}
                        >
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-left font-medium">
                            {item.property}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-left">
                            {item.type}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-left">
                            {item.client}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-center">
                            {item.units < 0
                              ? `(${Math.abs(item.units).toLocaleString()})`
                              : item.units.toLocaleString()}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-left">
                            {item.status}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-left">
                            {item.manager}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-center">
                            {item.pmFeePercent.toFixed(1)}%
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap border-r border-[#DEEFFF] text-center">
                            $ {item.pmFeePer25.toLocaleString()}
                          </td>
                          <td className="px-2 py-0 whitespace-nowrap text-center">
                            {item.annualPmFees < 0
                              ? `$ (${Math.abs(item.annualPmFees).toLocaleString()})`
                              : `$ ${item.annualPmFees.toLocaleString()}`}
                          </td>
                        </tr>
                      ))}

                      {/* Total Row */}
                      <tr className="font-bold text-[#43298F] border-b border-[#DEEFFF] bg-[#DEEFFF]">
                        <td className="px-2 py-0 whitespace-nowrap text-left font-bold border-r border-[#c7d2fe]">
                          Total
                        </td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap text-center font-bold border-r border-[#c7d2fe]">
                          {totalUnits.toLocaleString()}
                        </td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap border-r border-[#c7d2fe]"></td>
                        <td className="px-2 py-0 whitespace-nowrap text-center font-bold">
                          $ {totalAnnualPmFees.toLocaleString()}
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="text-center p-8 text-gray-500">
                  No data available for the selected filters
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Container>
  );
};

export default BusinessDevelopmentKPIs;
