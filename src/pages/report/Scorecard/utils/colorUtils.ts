export type MetricColor = 'green' | 'yellow' | 'red' | undefined;

export const getMetricColor = (
  value: number,
  isPositive: boolean,
): MetricColor => {
  if (isPositive && value > 0) return 'green';
  if (!isPositive && value < 0) return 'red';
  if (isPositive && value < 0) return 'red';
  if (!isPositive && value > 0) return 'green';
  return 'yellow';
};

export const getTextColorClass = (color?: MetricColor | string): string => {
  switch (color) {
    case 'green':
      return 'text-green-600';
    case 'yellow':
      return 'text-yellow-500';
    case 'red':
      return 'text-red-500';
    default:
      return 'text-gray-900';
  }
};

// Occupancy Metric: OCCUPANCY NON REV
// Occupancy Metric: SUBMARKET OCCUPANCY
export const getOccupancyPercentageColor = (value: number): MetricColor => {
  if (value < 90) return 'red';
  else if (value < 95) return 'yellow';
  else if (value <= 97) return 'green';
  else return 'yellow';
};

// Occupancy Metric: VARIANCE TO SUBMARKET
export const getPerformanceToSubmarketColor = (value: number): MetricColor => {
  if (!value) return undefined;
  return value > 0 ? 'green' : 'red';
};

// Occupancy Metric: OCCUPANCY TREND
export const getOccupancyTrendColor = (value: number): MetricColor => {
  if (value < 90) return 'red';
  else if (value <= 92) return 'yellow';
  else return 'green';
};

// Occupancy Metric: VARIANCE TO OCCUPANCY
export const getVarianceToOccupancyColor = (value: number): MetricColor => {
  const absValue = Math.abs(value);
  if (absValue > 5) return 'red';
  else if (absValue > 3) return 'yellow';
  else return 'green';
};

// Occupancy Metric: OCCUPANCY TREND T30 VAR
// (doubt: in the new logic, there is no dependency on anything other than the value)
export const getOccupancyTrendT30ChangeColor = (
  value: number,
  occupancy: number,
  submarketOccupancy: number,
): MetricColor => {
  if (occupancy >= submarketOccupancy) {
    return value >= 0 ? 'green' : 'red';
  } else {
    return value <= 0 ? 'red' : 'green';
  }
};

// Occupancy Metric: SHOWS T30
export const getShowsT30Color = (
  value: number,
  totalUnits?: number,
): MetricColor => {
  if (!totalUnits) return undefined;
  const threshold = totalUnits * (3 / 25);
  return value < threshold ? 'red' : 'green';
};

// Occupancy Metric: TREND GAIN/LOSS
// Occupancy Metric: GAIN/LOSS
export const getGainLossColor = (value: number): MetricColor => {
  const absValue = Math.abs(value);
  if (absValue > 3) return 'red';
  else if (absValue > 1) return 'yellow';
  else return 'green';
};

// Occupancy Metric: AGED VACANT UNITS
export const getAgedVacantUnitsColor = (
  agedVacantUnits: number,
  totalUnits: number,
): MetricColor => {
  if (totalUnits === 0) return undefined;
  const percentage = (agedVacantUnits / totalUnits) * 100;
  return percentage > 2 ? 'red' : undefined;
};

// Occupancy Metric: AVG AGED DAYS VACANT
export const getAvgDaysVacantColor = (value: number): MetricColor => {
  return value > 30 ? 'red' : undefined;
};

// Rental Metric: NEW NET IN PLACE RENT YoY CHANGE
// Rental Metric: RENEWAL NET IN PLACE RENT YoY CHANGE
// Rental Metric: NET IN PLACE RENT YoY CHANGE
export const getRentalYoYChangeColor = (value: number): MetricColor => {
  if (value < -5) return 'red';
  else if (value <= 5) return 'yellow';
  else return 'green';
};

// Rental Metric: YTD RENEWAL CONVERSION
export const getYTDRenewalConversionColor = (value: number): MetricColor => {
  if (value < 40) return 'red';
  else if (value <= 50) return 'yellow';
  else if (value <= 60) return 'green';
  else return undefined;
};

// Financial Metric: RENTAL INCOME
// Financial Metric: TOTAL INCOME
// Financial Metric: NOI
// Financial Metric: CONTROLLABLE NOI
export const getIncomeAndNOIColor = (
  value: number,
): MetricColor => {
  if (value <= -2) return 'red';
  else if (value < 2) return 'yellow';
  else return 'green';
};

// Financial Metric: CONTROLLABLE OP EX
// Financial Metric: TOTAL OP EX
export const getOpexColor = (value: number): MetricColor => {
  if (value >= 3) return 'red';
  else if (value > -3) return 'yellow';
  else return 'green';
};

// Financial Metric: CAPITAL (not being used now)
export const getCapitalColor = (value: number): MetricColor => {
  if (value >= 2) return 'green';
  if (value < 0) return 'yellow';
  if (value >= 0 && value < 2) return 'yellow';
  if (value <= -2) return 'red';
  return undefined;
};

// Performance Metric:  Collections % MTD
export const getCollectionsMTDColor = (value: number): MetricColor => {
  if (value < 85) return "red";
  else if (value < 98) return "yellow";
  else return "green";
};

// Performance Metric: Bad Debt W/O
export const getBadDebtWOColor = (value: number): MetricColor => {
  if (value < -2) return "red";
  else if (value < -1) return "yellow";
  else if (value <= 1) return "green";
  else return undefined;
};

// Performance Metric: Collection Recovery
export const getCollectionRecoveryRatioColor = (
  value: number,
  badDebtYTD: number = 0,
): MetricColor => {
  // Special case: if $0 Bad Debt YTD = Green
  if (badDebtYTD === 0) return 'green';

  if (value < 10) return "red";
  else if (value < 15) return "yellow";
  else return "green";
};

// Performance Metric: Turn Time
export const getAvgUnitTurnTimeColor = (value: number): MetricColor => {
  if (value >= 13) return 'red';
  else if (value > 10) return 'yellow';
  else return 'green';
};

// Performance Metric: Repeat Tickets
export const getRepeatServiceTicketsColor = (value: number): MetricColor => {
  if (value > 5) return 'red';
  else if (value >= 2) return 'yellow';
  else return 'green';
};

// Performance Metric: Tickets >72
export const getTicketsOver72HrsColor = (value: number): MetricColor => {
  if (value > 10) return 'red';
  else if (value > 5) return 'yellow';
  else return 'green';
};

// Performance Metric: Capital Execution
export const getCapitalExecutionColor = (value: number): MetricColor => {
  if (value > 130) return 'red';
  else if (value > 115) return 'yellow';
  else if (value >= 85) return 'green';
  else if (value >= 70) return 'yellow';
  else return 'red';
};

// Legacy functions for backward compatibility
export const getOccupancyColor = (value: number): MetricColor => {
  return getOccupancyPercentageColor(value);
};

export const getRentalYoYColor = (value: number): MetricColor => {
  return getRentalYoYChangeColor(value);
};

export const getCollectionColor = (value: number): MetricColor => {
  return getCollectionsMTDColor(value);
};

export const getPerformanceScoreIcon = (
  category: string,
  actualValue: string,
): string => {
  const isPercentage = actualValue.includes('%');
  const numValue = parseFloat(actualValue.replace(/[%$,]/g, ''));

  if (isNaN(numValue)) return '';

  const categoryLower = category.toLowerCase();

  if (categoryLower.includes('collection') && categoryLower.includes('mtd')) {
    // Value is already a percentage, don't multiply
    const color = getCollectionsMTDColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('bad debt')) {
    const color = getBadDebtWOColor(numValue);
    return getScoreIcon(color);
  }

  if (
    categoryLower.includes('collection') &&
    categoryLower.includes('recovery')
  ) {
    // Value is already a percentage, don't multiply
    const color = getCollectionRecoveryRatioColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (
    categoryLower.includes('unit turn') ||
    categoryLower.includes('turn time')
  ) {
    const color = getAvgUnitTurnTimeColor(numValue);
    return getScoreIcon(color);
  }

  if (categoryLower.includes('repeat service ticket')) {
    // Value is already a percentage, don't multiply
    const color = getRepeatServiceTicketsColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('tickets') && categoryLower.includes('72')) {
    // Value is already a percentage, don't multiply
    const color = getTicketsOver72HrsColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  if (categoryLower.includes('capital execution')) {
    // Value is already a percentage, don't multiply
    const color = getCapitalExecutionColor(
      isPercentage ? numValue : numValue * 100,
    );
    return getScoreIcon(color);
  }

  return '';
};

export const getScoreIcon = (score: MetricColor | string): string => {
  switch (score) {
    case 'green':
      return '🟢';
    case 'yellow':
      return '⚠️';
    case 'red':
      return '❌';
    default:
      return '';
  }
};

export const getMetricBackgroundColor = (label: string): string | undefined => {
  if (label === 'OCCUPANCY TREND T30 VAR' || label === 'SHOWS T30') {
    return '#F2F2F2';
  }
  return undefined;
};
