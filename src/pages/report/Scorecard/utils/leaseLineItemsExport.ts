import {
  alignmentCenter,
  alignmentLeft,
  alignmentRight,
  bodyRowCellFullBorders,
  columnHeadingFillFont,
  companyNameRowFont,
  excelLightPurpleColor,
  excelWhiteColor,
  filterPeriodRowFont,
  headingFill,
  reportTitleRowFont,
  rowColumnHeadingHeight,
} from '@/constants/exportExcelStyles';
import dayjs from 'dayjs';
import ExcelJS from 'exceljs';
import { toast } from 'sonner';
import { LeaseLineItem } from '@/types/leaseLineItemsTypes';

export const exportLeaseLineItemsToExcel = async (
  data: LeaseLineItem[],
  propertyCode: string,
  startDate: string,
  endDate: string,
  rentType: 'new' | 'renewal' | 'all',
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Lease Line Items');

  const rentTypeLabel = {
    new: 'New Net In Place Rent',
    renewal: 'Renewal Net In Place Rent',
    all: 'Net In Place Rent',
  };

  // Company name row
  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:L1');

  // Report title row
  const titleRow = worksheet.addRow([
    `${rentTypeLabel[rentType]} - Lease Line Items`,
  ]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:L2');

  // Period row
  const periodRow = worksheet.addRow([
    `For the Period ${dayjs(startDate).format('MM/DD/YYYY')} - ${dayjs(endDate).format('MM/DD/YYYY')}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:L3');

  // Property info row
  const propertyRow = worksheet.addRow([`Property: ${propertyCode}`]);
  propertyRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A4:L4');

  worksheet.addRow([]);

  const headers = [
    'Property Code',
    'Property Name',
    'Unit Code',
    'Lease From',
    'Lease To',
    'Lease #',
    'Square Feet',
    'Effective Rent',
    'One-Time Concession',
    'Recurring Concession',
    'Total Concession',
    'Amortized Concession',
  ];

  const headerRowData = worksheet.addRow(headers);
  headerRowData.height = rowColumnHeadingHeight;
  headerRowData.eachCell((cell, colNumber) => {
    cell.fill = headingFill;
    cell.font = columnHeadingFillFont;
    cell.alignment = {
      ...alignmentCenter,
      horizontal: colNumber === 2 ? 'left' : 'center',
    };
    cell.border = {
      top: { style: 'thin' },
      left: { style: 'thin' },
      bottom: { style: 'thin' },
      right: { style: 'thin' },
    };
  });

  worksheet.columns = [
    { width: 15 }, // Property Code
    { width: 25 }, // Property Name
    { width: 12 }, // Unit Code
    { width: 15 }, // Lease From
    { width: 15 }, // Lease To
    { width: 10 }, // Lease #
    { width: 12 }, // Square Feet
    { width: 15 }, // Effective Rent
    { width: 18 }, // One-Time Concession
    { width: 18 }, // Recurring Concession
    { width: 15 }, // Total Concession
    { width: 18 }, // Amortized Concession
  ];

  const formatCurrency = (value: number | null): string => {
    if (value === null || value === undefined) return '$0';
    const absValue = Math.abs(value);
    const formatted = absValue.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    });
    return value < 0 ? `($${formatted})` : `$${formatted}`;
  };

  const formatDate = (dateString: string): string => {
    if (!dateString) return '';
    return dayjs(dateString).format('MM/DD/YYYY');
  };

  data.forEach((item, index) => {
    const row = worksheet.addRow([
      item.property_code?.trim() || '',
      item.property_name?.trim() || '',
      item.unit_code?.trim() || '',
      formatDate(item.dtLeaseFrom),
      formatDate(item.dtLeaseTo),
      item.lease_no || '',
      item.dsqft || '',
      item.effective_rent,
      item.one_time_concession,
      item.recurring_concession,
      item.total_concession,
      item.amortized_concession,
    ]);

    const rowColor = index % 2 === 0 ? excelWhiteColor : excelLightPurpleColor;

    row.eachCell((cell, colNumber) => {
      cell.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: rowColor,
      };

      // Apply alignment
      if (colNumber === 2) {
        cell.alignment = alignmentLeft;
      } else if (colNumber >= 8 && colNumber <= 12) {
        cell.alignment = alignmentRight;
      } else {
        cell.alignment = alignmentCenter;
      }

      // Apply currency formatting
      if (colNumber >= 8 && colNumber <= 12) {
        cell.numFmt = '$#,##0;($#,##0)';
      }

      // Apply borders
      cell.border = bodyRowCellFullBorders;
    });
  });

  worksheet.addRow([]);

  // Summary section
  const summaryHeaderRow = worksheet.addRow(['Summary']);
  summaryHeaderRow.font = { ...reportTitleRowFont, size: 12 };
  summaryHeaderRow.fill = {
    type: 'pattern',
    pattern: 'solid',
    fgColor: excelLightPurpleColor,
  };
  worksheet.mergeCells(
    `A${summaryHeaderRow.number}:L${summaryHeaderRow.number}`,
  );

  const totalEffectiveRent = data.reduce(
    (sum, item) => sum + (item.effective_rent || 0),
    0,
  );
  const totalConcessions = data.reduce(
    (sum, item) => sum + (item.total_concession || 0),
    0,
  );
  const totalSquareFeet = data.reduce(
    (sum, item) => sum + (item.dsqft || 0),
    0,
  );
  const averageEffectiveRent = data.length > 0 
    ? totalEffectiveRent / data.length 
    : 0;

  const summaryRows = [
    ['Total Records:', data.length.toString()],
    ['Total Square Feet:', totalSquareFeet.toLocaleString()],
    ['Total Effective Rent:', formatCurrency(totalEffectiveRent)],
    ['Average Effective Rent:', formatCurrency(averageEffectiveRent)],
    ['Total Concessions:', formatCurrency(totalConcessions)],
    ['Generated On:', dayjs().format('MM/DD/YYYY HH:mm:ss')],
  ];

  summaryRows.forEach((rowData) => {
    const row = worksheet.addRow(rowData);
    row.font = { bold: true };
    row.getCell(1).alignment = alignmentLeft;
    row.getCell(2).alignment = alignmentRight;
  });

  const fileName = `LeaseLineItems_${rentTypeLabel[rentType].replace(/ /g, '_')}_${propertyCode}_${dayjs().format('YYYYMMDD_HHmmss')}.xlsx`;

  try {
    const buffer = await workbook.xlsx.writeBuffer();
    const blob = new Blob([buffer], {
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    a.click();
    window.URL.revokeObjectURL(url);

    toast.success(`Excel export successful: ${data.length} records exported`);
  } catch (error) {
    console.error('Error exporting to Excel:', error);
    toast.error('Failed to export data to Excel');
    throw error;
  }
};
