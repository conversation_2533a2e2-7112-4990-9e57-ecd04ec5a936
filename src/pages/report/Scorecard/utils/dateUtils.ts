import dayjs from 'dayjs';
import { ScoreCardData } from '@/types/scorecardTypes';

export const formatDateToMMDDYYYY = (date: dayjs.Dayjs): string => {
  return date.format('MM/DD/YYYY');
};

export const createDateFromApiFields = (
  month: number,
  day: number,
  year: number,
): dayjs.Dayjs => {
  return dayjs(
    `${year}-${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`,
  );
};

export const getAdjustedPeriodStartDate = (
  apiData: ScoreCardData | null,
): dayjs.Dayjs | null => {
  if (
    !apiData ||
    apiData.Adjusted_Period_Start_Month === undefined ||
    apiData.Adjusted_Period_Start_Day === undefined ||
    apiData.Adjusted_Period_Start_Year === undefined
  ) {
    return null;
  }

  return createDateFromApiFields(
    apiData.Adjusted_Period_Start_Month,
    apiData.Adjusted_Period_Start_Day,
    apiData.Adjusted_Period_Start_Year,
  );
};

export const getAdjustedPeriodEndDate = (
  apiData: ScoreCardData | null,
): dayjs.Dayjs | null => {
  if (
    !apiData ||
    apiData.Adjusted_Period_End_Month === undefined ||
    apiData.Adjusted_Period_End_Day === undefined ||
    apiData.Adjusted_Period_End_Year === undefined
  ) {
    return null;
  }

  return createDateFromApiFields(
    apiData.Adjusted_Period_End_Month,
    apiData.Adjusted_Period_End_Day,
    apiData.Adjusted_Period_End_Year,
  );
};

export const getDateRangeString = (
  startDate: dayjs.Dayjs,
  endDate: dayjs.Dayjs,
): string => {
  if (!startDate.isValid() || !endDate.isValid()) {
    return '0';
  }
  return `${formatDateToMMDDYYYY(startDate)} - ${formatDateToMMDDYYYY(endDate)}`;
};

export const calculateDateRange = (
  endDate: dayjs.Dayjs,
  daysBack: number,
): string => {
  if (!endDate.isValid()) {
    return '0';
  }
  const startDate = endDate.subtract(daysBack, 'day');
  return getDateRangeString(startDate, endDate);
};

export const getFirstWeekOfPreviousMonth = (
  referenceDate: dayjs.Dayjs = dayjs(),
): { startDate: dayjs.Dayjs; endDate: dayjs.Dayjs } => {
  const firstDayOfPreviousMonth = referenceDate
    .subtract(1, 'month')
    .startOf('month');

  const firstDayWeekday = firstDayOfPreviousMonth.day();
  const daysToFirstMonday = firstDayWeekday === 0 ? 1 : 8 - firstDayWeekday;

  let firstMonday = firstDayOfPreviousMonth.add(daysToFirstMonday, 'day');

  if (firstMonday.date() > 7) {
    const daysToSubtract = firstDayWeekday === 0 ? 6 : firstDayWeekday - 1;
    firstMonday = firstDayOfPreviousMonth.subtract(daysToSubtract, 'day');
  }

  const firstSunday = firstMonday.add(6, 'day');

  return {
    startDate: firstMonday,
    endDate: firstSunday,
  };
};

export const getPerformanceTableHeaders = (
  apiData: ScoreCardData | null,
  filterEndDate: string,
): {
  table1Headers: string[];
  table2Headers: string[];
} => {
  const adjustedStartDate = getAdjustedPeriodStartDate(apiData);
  const adjustedEndDate = getAdjustedPeriodEndDate(apiData);

  // For fallback, use yesterday's date (same as default filters)
  const defaultEndDate = dayjs().subtract(1, 'day');

  const table1Headers = [
    // Always use filter end date for Collections % MTD (first row), or default to yesterday
    filterEndDate && dayjs(filterEndDate).isValid()
      ? formatDateToMMDDYYYY(dayjs(filterEndDate))
      : formatDateToMMDDYYYY(defaultEndDate),

    adjustedStartDate &&
    adjustedEndDate &&
    adjustedStartDate.isValid() &&
    adjustedEndDate.isValid()
      ? getDateRangeString(adjustedStartDate, adjustedEndDate)
      : '0',

    adjustedStartDate &&
    adjustedEndDate &&
    adjustedStartDate.isValid() &&
    adjustedEndDate.isValid()
      ? getDateRangeString(adjustedStartDate, adjustedEndDate)
      : '0',
  ];

  // Use filter end date for the first three rows of table 2, or default to yesterday
  const dateForTable2 =
    filterEndDate && dayjs(filterEndDate).isValid()
      ? dayjs(filterEndDate)
      : defaultEndDate;

  const table2Headers = [
    dateForTable2.isValid() ? calculateDateRange(dateForTable2, 90) : '0',

    dateForTable2.isValid() ? calculateDateRange(dateForTable2, 30) : '0',

    dateForTable2.isValid() ? calculateDateRange(dateForTable2, 30) : '0',

    adjustedStartDate &&
    adjustedEndDate &&
    adjustedStartDate.isValid() &&
    adjustedEndDate.isValid()
      ? getDateRangeString(adjustedStartDate, adjustedEndDate)
      : '0',
  ];

  return {
    table1Headers,
    table2Headers,
  };
};
