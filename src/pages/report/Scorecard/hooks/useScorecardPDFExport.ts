import { RefObject, useCallback, useState } from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { toast } from 'sonner';
import { FilterValues } from '../components/filters';
import {
  // PDF_EXPORT_DIMENSIONS,
  PDF_EXPORT_DIMENSIONS_A3,
} from '../constants/scorecardConstants';
import { exportScorecardToPDF } from '../utils/pdfExport';
import {
  captureElementAsImage,
  debugImageLoadingIssues,
  validateScreenshot,
} from '../utils/screenshotUtils';

export const useScorecardPDFExport = () => {
  const [isCapturing, setIsCapturing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const {
    propertyInfo,
    occupancyMetrics,
    rentalMetrics,
    financialMetrics,
    performanceRows,
    reputationMetrics,
    summaryTexts,
    ytdTurnCost,
    financialDateRange,
    rawApiData,
    loading,
    loadingStates,
  } = useSelector((state: RootState) => state.scorecard);

  const exportToPDF = useCallback(
    async (
      filters: FilterValues,
      contentRef: RefObject<HTMLDivElement | null>,
    ) => {
      // Check if any critical data is still loading
      const isAnyDataLoading =
        loading ||
        loadingStates.propertyInfo ||
        loadingStates.occupancyMetrics ||
        loadingStates.rentalMetrics ||
        loadingStates.financialMetrics ||
        loadingStates.performanceRows ||
        loadingStates.reputationMetrics ||
        loadingStates.summaryTexts;

      if (isAnyDataLoading) {
        console.warn('Cannot export PDF while data is still loading');
        return;
      }

      if (!contentRef.current) {
        console.error('Content reference not available for screenshot');
        toast.error('Unable to capture content for PDF export');
        return;
      }

      if (isCapturing || isGenerating) {
        console.warn('PDF export already in progress');
        return;
      }

      try {
        setIsCapturing(true);
        toast.info('Capturing content...');

        debugImageLoadingIssues(contentRef.current);

        const screenshotResult = await captureElementAsImage(
          contentRef.current,
          {
            scale: PDF_EXPORT_DIMENSIONS_A3.scale,
            backgroundColor: '#f8fafc',
            useCORS: true,
            allowTaint: false,
            windowWidth: PDF_EXPORT_DIMENSIONS_A3.windowWidth,
            windowHeight: PDF_EXPORT_DIMENSIONS_A3.windowHeight,
            scrollX: 0,
            scrollY: 0,
          },
        );

        if (!screenshotResult.success || !screenshotResult.dataUrl) {
          throw new Error(
            screenshotResult.error || 'Failed to capture screenshot',
          );
        }

        if (!validateScreenshot(screenshotResult.dataUrl)) {
          throw new Error('Captured screenshot appears to be invalid');
        }

        setIsCapturing(false);
        setIsGenerating(true);
        toast.info('Generating PDF...');

        const pdfData = {
          propertyInfo,
          occupancyMetrics,
          rentalMetrics,
          financialMetrics,
          performanceRows,
          reputationMetrics,
          summaryTexts,
          ytdTurnCost,
          financialDateRange,
          rawApiData,
          filters,
          screenshotDataUrl: screenshotResult.dataUrl,
        };

        await exportScorecardToPDF(pdfData);
        toast.success('PDF export completed successfully!');
      } catch (error) {
        console.error('PDF export failed:', error);
        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error occurred';
        toast.error(`PDF export failed: ${errorMessage}`);
      } finally {
        setIsCapturing(false);
        setIsGenerating(false);
      }
    },
    [
      propertyInfo,
      occupancyMetrics,
      rentalMetrics,
      financialMetrics,
      performanceRows,
      reputationMetrics,
      summaryTexts,
      ytdTurnCost,
      financialDateRange,
      rawApiData,
      loading,
      loadingStates,
      isCapturing,
      isGenerating,
    ],
  );

  const canExport = useCallback(() => {
    const isAnyDataLoading =
      loading ||
      loadingStates.propertyInfo ||
      loadingStates.occupancyMetrics ||
      loadingStates.rentalMetrics ||
      loadingStates.financialMetrics ||
      loadingStates.performanceRows ||
      loadingStates.reputationMetrics ||
      loadingStates.summaryTexts;

    return (
      !isAnyDataLoading &&
      !!propertyInfo.propertyName &&
      !isCapturing &&
      !isGenerating
    );
  }, [
    loading,
    loadingStates,
    propertyInfo.propertyName,
    isCapturing,
    isGenerating,
  ]);

  const getExportStatus = useCallback(() => {
    if (isCapturing) return 'Capturing...';
    if (isGenerating) return 'Generating PDF...';
    return 'Export PDF';
  }, [isCapturing, isGenerating]);

  return {
    exportToPDF,
    canExport,
    getExportStatus,
    isCapturing,
    isGenerating,
  };
};
