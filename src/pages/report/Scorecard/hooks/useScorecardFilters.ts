import { useCallback, useState } from 'react';
import { getScoreCardFilterOptions } from '@/api/scorecardApi';
import { setFilterOptions, setFilters } from '@/slice/scoreCardSlice';
import { RootState } from '@/store';
import dayjs from 'dayjs';
import { useDispatch, useSelector } from 'react-redux';
import { FilterValues } from '../components/filters';
import { DateOption, FilterOption } from '../types/scorecardTypes';

export const useScorecardFilters = () => {
  const dispatch = useDispatch();
  const {
    filters,
    availableProperties,
    availableSystems,
    availableDates,
    rawSystemsData,
    minDate,
    maxDate,
  } = useSelector((state: RootState) => state.scorecard);

  const [isLoadingFilterOptions, setIsLoadingFilterOptions] = useState(false);
  const [filterOptionsError, setFilterOptionsError] = useState<string | null>(
    null,
  );

  const processFilterOptions = useCallback(
    (response: {
      data?: {
        properties?: Array<{ BU: string; Property: string }>;
        systems?: Array<{
          System: string;
          BU: string;
          property_hmy: string;
          PropertyName: string;
        }>;
        dates?: Array<{ month_name: string; year: number; date: string }>;
      };
    }) => {
      setIsLoadingFilterOptions(false);

      const properties: FilterOption[] =
        response?.data?.properties?.map(
          (p: { BU: string; Property: string }) => ({
            label: `${p.BU} - ${p.Property}`,
            value: `${p.BU} - ${p.Property}`,
          }),
        ) || [];

      const systems: FilterOption[] =
        response?.data?.systems?.map(
          (s: {
            System: string;
            BU: string;
            property_hmy: string;
            PropertyName: string;
          }) => ({
            label: s.System,
            value: s.System,
          }),
        ) || [];

      const rawSystemsData = response?.data?.systems || [];

      const dates: DateOption[] =
        response?.data?.dates?.map(
          (d: { month_name: string; year: number; date: string }) => ({
            label: `${d.month_name} ${d.year}`,
            value: dayjs(d.date).format('YYYY-MM-DD'),
            dayjsValue: dayjs(d.date),
            monthKey: dayjs(d.date).format('YYYY-MM'),
          }),
        ) || [];

      // Remove duplicates
      const uniqueSystems = systems.filter(
        (system, index, self) =>
          index === self.findIndex((s) => s.value === system.value),
      );

      const uniqueDates = dates.filter(
        (date, index, self) =>
          index === self.findIndex((d) => d.value === date.value),
      );

      // Calculate min and max dates
      const calculatedMinDate =
        uniqueDates.length > 0
          ? uniqueDates.reduce((min, curr) =>
              curr.dayjsValue &&
              (!min.dayjsValue || curr.dayjsValue.isBefore(min.dayjsValue))
                ? curr
                : min,
            )
          : null;

      const calculatedMaxDate =
        uniqueDates.length > 0
          ? uniqueDates.reduce((max, curr) =>
              curr.dayjsValue &&
              (!max.dayjsValue || curr.dayjsValue.isAfter(max.dayjsValue))
                ? curr
                : max,
            )
          : null;

      dispatch(
        setFilterOptions({
          properties,
          systems: uniqueSystems,
          dates: uniqueDates,
          rawSystemsData,
          minDate: calculatedMinDate?.dayjsValue || null,
          maxDate: calculatedMaxDate?.dayjsValue || null,
        }),
      );
    },
    [dispatch],
  );

  const loadFilterOptions = useCallback(async () => {
    setIsLoadingFilterOptions(true);
    setFilterOptionsError(null);

    try {
      const response = await getScoreCardFilterOptions();
      processFilterOptions(response);
      return response;
    } catch (error) {
      console.error('Error loading filter options:', error);
      setFilterOptionsError('Failed to load filter options');
      return null;
    } finally {
      setIsLoadingFilterOptions(false);
    }
  }, [processFilterOptions]);

  const updateFilters = useCallback(
    (newFilters: FilterValues) => {
      const propertyCode = newFilters.property.split(' - ')[0];

      let reportingPeriod = '';
      if (newFilters.startDate && newFilters.endDate) {
        const startDate = new Date(newFilters.startDate);
        const endDate = new Date(newFilters.endDate);

        if (!isNaN(startDate.getTime()) && !isNaN(endDate.getTime())) {
          reportingPeriod = `${startDate.getMonth() + 1}/${startDate.getDate()}/${startDate.getFullYear()} - ${endDate.getMonth() + 1}/${endDate.getDate()}/${endDate.getFullYear()}`;
        }
      }

      dispatch(
        setFilters({
          ...newFilters,
          reportingPeriod,
        }),
      );

      return {
        propertyCode,
        startDate: newFilters.startDate,
        endDate: newFilters.endDate,
      };
    },
    [dispatch],
  );

  const setFilterOptionsLoading = useCallback((loading: boolean) => {
    setIsLoadingFilterOptions(loading);
  }, []);

  return {
    filters,
    availableProperties,
    availableSystems,
    availableDates,
    rawSystemsData,
    minDate,
    maxDate,
    isLoadingFilterOptions,
    filterOptionsError,
    updateFilters,
    loadFilterOptions,
    processFilterOptions,
    setFilterOptionsLoading,
  };
};
