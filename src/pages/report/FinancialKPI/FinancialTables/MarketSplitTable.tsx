import { ReportFilters } from '@/slice/incomeReportSlice';
import FinancialMarketTable from '../components/FinancialMarketTable';
import {
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
} from '@/api/financeKPIRegionApi/financeKPIRegionApi.types';
import {
  getPropMgmtData,
  getRORData,
  getFeesUnitData,
  getCostUnitData,
  getWAUData,
  getActualUnitsData,
} from '../utils/helperFinanceKpi';
import React from 'react';

interface PropsTypes {
  actualData: FinanceKPIRegionActualResponse[];
  forecastData: FinanceKPIRegionForecastResponse[];
  filters: ReportFilters;
  datePeriod: string;
}

const MarketSplitTable: React.FC<PropsTypes> = ({ actualData, forecastData, datePeriod }) => {

  const propertyManagementData = getPropMgmtData(actualData, forecastData);
  const rorData = getRORData(actualData, forecastData);
  const feesUnit = getFeesUnitData(actualData, forecastData);
  const costUnit = getCostUnitData(actualData, forecastData);
  const wauData = getWAUData(actualData, forecastData);
  const actualUnitsData = getActualUnitsData(actualData, forecastData);

  return (
    <>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
      </div>

      <FinancialMarketTable
        tableHeading1="Property Management Fees"
        tableData1={propertyManagementData}
        tableHeading2="Return On Revenue"
        tableData2={rorData}
      />
      <div className="my-6">
        <FinancialMarketTable
          tableHeading1="Fee/Unit"
          tableData1={feesUnit}
          tableHeading2="Cost/Unit"
          tableData2={costUnit}
        />
      </div>

      <FinancialMarketTable
        tableHeading1="WAU"
        tableData1={wauData}
        tableHeading2="Actual Units"
        tableData2={actualUnitsData}
      />
    </>
  );
}

export default MarketSplitTable;
