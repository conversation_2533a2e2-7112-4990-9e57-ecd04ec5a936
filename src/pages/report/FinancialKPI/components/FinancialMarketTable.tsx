import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableColumnSeperateCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { ReportRowNoData } from '../../ReportsCommonComponents/ReportRowNoData';
import { FinacialMarketTableDataFormatedTypes } from '../FinancialTables/MarketSplitTable';
import { formatValue } from '../utils/helperFinanceKpi';

export interface FinancialMarketTableProps {
  tableHeading1: string;
  tableHeading2: string;
  tableData1: FinacialMarketTableDataFormatedTypes[];
  tableData2: FinacialMarketTableDataFormatedTypes[];
}

const columnsWidth = 'min-w-[105px] max-w-[105px]';

export default function FinancialMarketTable(props: FinancialMarketTableProps) {
  const { tableHeading1, tableHeading2, tableData1 = [], tableData2 } = props;

  interface GetMergedTableDataTypes {
    regionmarket: string;
    table1: FinacialMarketTableDataFormatedTypes | null;
    table2: FinacialMarketTableDataFormatedTypes | null;
  }

  const getMergedTableData = (
    data1: FinacialMarketTableDataFormatedTypes[],
    data2: FinacialMarketTableDataFormatedTypes[],
  ): GetMergedTableDataTypes[] => {
    const map1 = new Map(data1.map((d) => [d.regionmarket, d]));
    const map2 = new Map(data2.map((d) => [d.regionmarket, d]));

    // Get all unique regionmarkets
    let allRegionMarkets = [];
    if (data1?.length >= data2?.length) {
      allRegionMarkets = Array.from(new Set([...map1.keys(), ...map2.keys()]));
    } else {
      allRegionMarkets = Array.from(new Set([...map2.keys(), ...map1.keys()]));
    }

    return allRegionMarkets.map((regionmarket) => {
      return {
        regionmarket,
        table1: map1.get(regionmarket) || null,
        table2: map2.get(regionmarket) || null,
      };
    });
  };

  const mergedData = getMergedTableData(tableData1, tableData2);

  return (
    <div>
      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow>
            <th className={`${columnsWidth}`}></th>

            <CommonTableHeadingMergeCell colSpan={6}>
              {tableHeading1}
            </CommonTableHeadingMergeCell>

            <CommonTableColumnSeperateCell />

            <CommonTableHeadingMergeCell colSpan={6}>
              {tableHeading2}
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            <th></th>

            <CommonTableHeadingCell
              borderLeft
              colSpan={3}
              textAlign="text-center"
              fontSize="text-sm"
            >
              YTD Act. Vs. Budget
            </CommonTableHeadingCell>

            <CommonTableHeadingCell
              borderRight
              colSpan={3}
              textAlign="text-center"
              fontSize="text-sm"
            >
              Full Year Exp. Vs. Budget
            </CommonTableHeadingCell>

            <CommonTableColumnSeperateCell />

            <CommonTableHeadingCell
              borderLeft
              colSpan={3}
              textAlign="text-center"
              fontSize="text-sm"
            >
              YTD Act. Vs. Budget
            </CommonTableHeadingCell>

            <CommonTableHeadingCell
              borderRight
              colSpan={3}
              textAlign="text-center"
              fontSize="text-sm"
            >
              Full Year Exp. Vs. Budget
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>

          <CommonTableSubHeaderRow>
            <th className={`${columnsWidth}`}></th>
            <CommonTableHeadingCell
              borderLeft
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Actuals
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Budget
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Variance
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Expected
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Budget
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
              borderRight
            >
              Variance
            </CommonTableHeadingCell>

            <CommonTableColumnSeperateCell />

            <CommonTableHeadingCell
              borderLeft
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Actuals
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Budget
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Variance
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Expected
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
            >
              Budget
            </CommonTableHeadingCell>
            <CommonTableHeadingCell
              className={`${columnsWidth} border-t-[1px] border-black`}
              borderRight
            >
              Variance
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {/* {tableData1?.length === 0 && <ReportRowNoData colSpan={18} />} */}
          {mergedData?.length === 0 && <ReportRowNoData colSpan={18} />}

          {mergedData?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.regionmarket}>
                <CommonTableBodyCell
                  textAlign="text-start"
                  borderRight
                  className={`min-w-[150px] max-w-[150px] `}
                >
                  {item?.regionmarket}
                </CommonTableBodyCell>

                <CommonTableBodyCell>
                  {formatValue(item?.table1?.actual, item?.table1?.unit ?? '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(item?.table1?.budget, item?.table1?.unit ?? '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {formatValue(
                    item?.table1?.variance,
                    item?.table1?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(
                    item?.table1?.forecast,
                    item?.table1?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(
                    item?.table1?.budget_forecast,
                    item?.table1?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {formatValue(
                    item?.table1?.variance_forecast,
                    item?.table1?.unit ?? '',
                  )}
                </CommonTableBodyCell>

                <CommonTableColumnSeperateCell />

                <CommonTableBodyCell borderLeft>
                  {formatValue(item?.table2?.actual, item?.table2?.unit ?? '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(item?.table2?.budget, item?.table2?.unit ?? '')}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {formatValue(
                    item?.table2?.variance,
                    item?.table2?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(
                    item?.table2?.forecast,
                    item?.table2?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {formatValue(
                    item?.table2?.budget_forecast,
                    item?.table2?.unit ?? '',
                  )}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight>
                  {formatValue(
                    item?.table2?.variance_forecast,
                    item?.table2?.unit ?? '',
                  )}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
        </tbody>
      </CommonTable>

      {/*  */}
    </div>
  );
}
