import {
  FinanceKPIRegionActualResponse,
  FinanceKPIRegionForecastResponse,
  FinanceKPIRegionWiseResponce,
  FinacialMarketTableDataFormatedTypes,
} from "@/api/financeKPIRegionApi/financeKPIRegionApi.types";

export const regionSplitRowOrderTable = [
  { label: 'PM Revenue', key: 'PM Fees', unit: '' },
  { label: 'ROR', key: 'ROR', unit: '%' },
  { label: 'Fee/Unit', key: 'Fees/Unit', unit: '' },
  { label: 'Cost/Unit', key: 'Cost/Unit', unit: '' },
  { label: 'WAU', key: 'WAU', unit: '' },
  { label: 'Actual Units', key: 'Actual Units', unit: '' },
];
// ?.sort((a, b)=>a.label.localeCompare(b.label));

export function formatValue(
  value: number | undefined | null,
  unit: string,
  forExcel: boolean = false,
) {
  if (value === undefined || value === null) return '-';

  const rounded = Math.round(value);

  if (forExcel) {
    if (unit === '%') {
      // return rounded / 100;
      return value / 100;
    }
    return rounded;
  }

  if (unit === '$') {
    if (rounded < 0) {
      return `($${Math.abs(rounded).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;
    } else {
      return `$${rounded.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
    }
  }

  if (unit === '%') {
    if (value < 0) {
      return `(${(Math.abs(value) ?? 0).toFixed(0)}%)`;
      // return `(${(Math.abs(value) ?? 0).toFixed(1)}%)`;
    } else {
      return `${(value ?? 0).toFixed(0)}%`;
      // return `${(value ?? 0).toFixed(1)}%`;
    }
  }

  if (rounded < 0) {
    return `(${Math.abs(rounded).toLocaleString('en-US', { maximumFractionDigits: 0 })})`;
  } else {
    return `${rounded.toLocaleString('en-US', { maximumFractionDigits: 0 })}`;
  }
}

export const getPropMgmtData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData
    ?.map((actual) => {
      const forecast = forecastData.find(
        (f) => f.regionmarket === actual.regionmarket,
      );
      return {
        regionmarket: actual.regionmarket,
        actual: actual['PM Fees_Actual'],
        budget: actual['PM Fees_Budget'],
        variance: actual['PM Fees_Varriance'],
        forecast: forecast?.['PM Fees_Forecast'],
        budget_forecast: forecast?.['PM Fees_Budget'],
        variance_forecast: forecast?.['PM Fees_Variance'],
        unit: '',
      } as FinacialMarketTableDataFormatedTypes;
    })
    ?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));
};

export const getRORData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['ROR_Actual'],
      budget: actual['ROR_Budget'],
      variance: actual['ROR_Varriance'],
      forecast: forecast?.['ROR_Forecast'],
      budget_forecast: forecast?.['ROR_Budget'],
      variance_forecast: forecast?.['ROR_Variance'],
      unit: '%',
    } as FinacialMarketTableDataFormatedTypes;
  })?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));
};

export const getFeesUnitData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Fees/Unit_Actual'],
      budget: actual['Fees/Unit_Budget'],
      variance: actual['Fees/Unit_Varriance'],
      forecast: forecast?.['Fees/Unit_Forecast'],
      budget_forecast: forecast?.['Fees/Unit_Budget'],
      variance_forecast: forecast?.['Fees/Unit_Variance'],
      unit: '',
    } as FinacialMarketTableDataFormatedTypes;
  })?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));;
};

export const getCostUnitData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Cost/Unit_Actual'],
      budget: actual['Cost/Unit_Budget'],
      variance: actual['Cost/Unit_Varriance'],
      forecast: forecast?.['Cost/Unit_Forecast'],
      budget_forecast: forecast?.['Cost/Unit_Budget'],
      variance_forecast: forecast?.['Cost/Unit_Variance'],
      unit: '',
    } as FinacialMarketTableDataFormatedTypes;
  })?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));
};

export const getWAUData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['WAU_Actual'],
      budget: actual['WAU_Budget'],
      variance: actual['WAU_Varriance'],
      forecast: forecast?.['WAU_Forecast'],
      budget_forecast: forecast?.['WAU_Budget'],
      variance_forecast: forecast?.['WAU_Variance'],
      unit: '',
    } as FinacialMarketTableDataFormatedTypes;
  })?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));
};

export const getActualUnitsData: (actualData: FinanceKPIRegionActualResponse[], forecastData: FinanceKPIRegionForecastResponse[]) => FinacialMarketTableDataFormatedTypes[] = (actualData, forecastData) => {
  return actualData.map((actual) => {
    const forecast = forecastData.find(
      (f) => f.regionmarket === actual.regionmarket,
    );
    return {
      regionmarket: actual.regionmarket,
      actual: actual['Actual Units_Actual'],
      budget: actual['Actual Units_Budget'],
      variance: actual['Actual Units_Varriance'],
      forecast: forecast?.['Actual Units_Forecast'],
      budget_forecast: forecast?.['Actual Units_Budget'],
      variance_forecast: forecast?.['Actual Units_Variance'],
      unit: '',
    } as FinacialMarketTableDataFormatedTypes;
  })?.sort((a, b) => a.regionmarket.localeCompare(b.regionmarket));;
};

export const getGroupData: (tableData: FinanceKPIRegionWiseResponce[]) => Record<string, Record<string, (typeof tableData)[number]>> = (tableData) => {
  return tableData
    ?.filter((item) => item.Region !== 'National')
    ?.reduce(
      (acc, curr) => {
        if (!acc[curr.Region]) acc[curr.Region] = {};
        acc[curr.Region][curr.FeesType] = curr;
        return acc;
      },
      {} as Record<string, Record<string, (typeof tableData)[number]>>,
    );
};
