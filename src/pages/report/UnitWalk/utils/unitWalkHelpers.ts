import {
  UnitWalkKPIRegionTypes,
  UnitWalkKPIYTDMarketTypes,
} from '@/api/unitWalkApis/unitWalkTypes';

export function sumUnitWalkRegionColumns(
  data: UnitWalkKPIRegionTypes[],
  keys: ReadonlyArray<keyof UnitWalkKPIRegionTypes>,
): Record<keyof UnitWalkKPIRegionTypes, number> {
  const totals = {} as Record<keyof UnitWalkKPIRegionTypes, number>;

  keys.forEach((key) => {
    totals[key] = data.reduce((sum, item) => {
      const value = item[key];
      return sum + (typeof value === 'number' ? value : 0);
    }, 0);
  });
  return totals;
}

export const sumUnitWalkMarket = (data: UnitWalkKPIYTDMarketTypes[]) => {
  return {
    beginning: data.reduce((sum, item) => sum + (item?.beginning ?? 0), 0),
    additions: data.reduce((sum, item) => sum + (item?.additions ?? 0), 0),
    losses: data.reduce((sum, item) => sum + (item?.losses ?? 0), 0),
    current: data.reduce((sum, item) => sum + (item?.current ?? 0), 0),
  };
};
