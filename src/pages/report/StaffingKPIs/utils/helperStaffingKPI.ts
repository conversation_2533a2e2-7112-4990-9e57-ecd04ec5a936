import {
  StaffingMarketKPITypes,
  StaffingRegionalKPITypes,
  StaffingRPMKPITypes,
} from '@/api/staffingKpi/staffingKpiApi.types';

export const tableRowOrderFormateStaffingRegional = [
  { label: "RPM's", key: 'RPMs' },
  { label: "VP's", key: 'VPs' },
  { label: 'Properties', key: 'Properties' },
  { label: 'Properties/RPM', key: 'Properties/RPM', unit: '%' },
  // { label: 'Workload/RPM', key: 'Workload/RPM', },
  { label: "RPM's per VP", key: 'RPMs per VP', unit: '%' },
]?.sort((a, b)=>a.label.localeCompare(b.label));

export const getStaffingPropertyCountColumnsTotals = (
  data: StaffingRPMKPITypes[],
) => {
  return {
    stabilized: data?.reduce((sum, item) => sum + (item.Stabilised ?? 0), 0),
    lease_up: data?.reduce((sum, item) => sum + (item.Lease_Up ?? 0), 0),
    total_properties: data?.reduce(
      (sum, item) => sum + (item.Total_Properties ?? 0),
      0,
    ),
    expected_pm_fees: data?.reduce(
      (sum, item) => sum + (item.Expected_PM_Fees ?? 0),
      0,
    ),
  };
};

export function getStaffingRegionalColumnTotals(
  data: StaffingRegionalKPITypes[],
) {
  return {
    central_actual: data.reduce(
      (sum, item) => sum + (item.central_actual ?? 0),
      0,
    ),
    east_actual: data.reduce((sum, item) => sum + (item.east_actual ?? 0), 0),
    west_actual: data.reduce((sum, item) => sum + (item.west_actual ?? 0), 0),
    consolidated_actual: data.reduce(
      (sum, item) => sum + (item.consolidated_actual ?? 0),
      0,
    ),
  };
}

export const getStaffingMarketColumnTotals = (
  marketTableData: StaffingMarketKPITypes[],
) => {
  const dataLength = marketTableData?.length || 0; // Get the number of items, default to 0 if null/undefined

  if (dataLength === 0) {
    // Return 0 for all averages if there's no data to avoid division by zero
    return {
      rpm_actual: 0,
      vp_actual: 0,
      properties_actual: 0,
      properties_rpm_actual: 0,
      rpm_vp_actual: 0,
    };
  }

  return {
    rpm_actual:
      marketTableData.reduce((sum, item) => sum + (item?.RPM_Actual ?? 0), 0)  ,
    vp_actual:
      marketTableData.reduce((sum, item) => sum + (item?.VP_Actual ?? 0), 0)  ,
    properties_actual:
      marketTableData.reduce(
        (sum, item) => sum + (item?.Property_Actual ?? 0),
        0,
      )  ,
    properties_rpm_actual:
      marketTableData.reduce(
        (sum, item) => sum + (item?.PropertyPerRPM_Actual ?? 0),
        0,
      ) / dataLength,
    // workload_rpm_actual: marketTableData?.reduce(
    //   (sum, item) => sum + (item?.WorkloadPerRPM_Actual ?? 0),
    //   0,
    // ) / dataLength, // Uncomment if needed, applying the same logic
    rpm_vp_actual:
      marketTableData.reduce((sum, item) => sum + (item?.RPMPerVP_Actual ?? 0), 0) /
      dataLength,
  };
};
