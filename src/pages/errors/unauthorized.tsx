import { ShieldOff } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function UnauthorizedPage() {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col items-center justify-center min-h-screen min-w-screen bg-gradient-to-br from-gray-50 to-gray-100">
      {/* Willowbridge Logo and Branding */}
      <div className="mb-8 text-center">
        <img
          src={toAbsoluteUrl('/media/logo/logo1-light.png')}
          alt="Willowbridge Logo"
          className="h-16 mx-auto mb-4"
          style={{ objectFit: 'contain' }}
        />
        <h1 className="text-2xl font-semibold text-[#43298F] mb-2">
          Report Portal
        </h1>
      </div>

      <Card className="w-full max-w-md shadow-lg border-[#DEEFFF]">
        <CardHeader className="text-center pb-4">
          <div className="flex justify-center mb-4">
            <div className="p-3 rounded-full bg-gradient-to-r from-red-500 to-red-600 shadow-md">
              <ShieldOff className="h-8 w-8 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-[#43298F]">
            Access Denied
          </CardTitle>
        </CardHeader>
        <CardContent className="text-center pt-0">
          <p className="text-gray-600 mb-6 leading-relaxed">
            You don't have permission to access this page. Please contact your
            administrator if you believe this is an error.
          </p>
          <Button
            onClick={() => navigate('/')}
            className="w-full bg-gradient-to-r from-[#43298F] to-[#5a4db0] hover:from-[#5a4db0] hover:to-[#43298F] transition-all duration-300 shadow-md"
          >
            Return to Dashboard
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}
