import { ReactNode } from 'react';
import { RootState } from '@/store';
import { useSelector } from 'react-redux';
import { Navigate, useLocation } from 'react-router-dom';

interface RoleBasedRouteProps {
  children: ReactNode;
  allowedRoles?: ((userRoles: string[] | null) => boolean)[];
  redirectTo?: string;
}

export function RoleBasedRoute({
  children,
  allowedRoles = [],
  redirectTo = '/error/unauthorized',
}: RoleBasedRouteProps) {
  const userRoles = useSelector((state: RootState) => state.auth.userRole);
  const location = useLocation();

  if (allowedRoles.length === 0) {
    return <>{children}</>;
  }

  const hasAccess = allowedRoles.some((roleCheck) => roleCheck(userRoles));

  if (!hasAccess) {
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return <>{children}</>;
}
