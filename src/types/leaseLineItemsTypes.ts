export interface LeaseLineItemRequest {
  startDate: string;
  endDate: string;
  propertyCode: string;
  rnkFilter: '1' | '>1' | '>=1';
}

export interface LeaseLineItem {
  property_code: string;
  property_name: string;
  unit_code: string;
  dtLeaseFrom: string;
  dtLeaseTo: string;
  lease_no: number;
  dsqft: number;
  effective_rent: number;
  one_time_concession: number | null;
  recurring_concession: number | null;
  total_concession: number;
  amortized_concession: number | null;
}

export interface LeaseLineItemsResponse {
  data: LeaseLineItem[];
}