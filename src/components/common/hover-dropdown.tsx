// Libraries
import { type MenuProps } from 'antd';
import { Dropdown } from "antd";
import type { DropdownProps } from 'antd';
import {
  RiContractRightLine,
  RiFileExcel2Line,
  RiFilePdf2Line,
} from '@remixicon/react';
import { cva } from 'class-variance-authority';
import { cn } from '@/lib/utils';

type HoverDropdownProps = DropdownProps & {
  size?: 'small' | 'middle' | 'large';
  onExportExcel?: () => void;
  onExportPDF?: () => void;
};

const hoverDropdownVariants = cva(
  'border border-[#43298F] bg-white text-[#43298F] hover:bg-[#DEDEDE]',
  {
    variants: {
      size: {
        small: 'px-[5px] py-1 rounded-sm',
        middle: 'px-6 py-2 rounded-sm',
        large: 'px-2 py-[7px] rounded-md',
      },
    },
    defaultVariants: {
      size: 'middle',
    },
  }
);

const hoverDropdownIconVariants = cva(
  'mt-[2px] rotate-90 text-[#43298F]',
  {
    variants: {
      size: {
        small: 'w-4 h-4',
        middle: 'w-5 h-5',
        large: 'w-6 h-6 mt-[2px]',
      },
    },
    defaultVariants: {
      size: 'middle',
    },
  },
);

export const HoverDropdown: React.FC<HoverDropdownProps> = ({
  size = 'middle',
  onExportExcel,
  onExportPDF,
  ...props
}) => {

  const items: MenuProps['items'] = [
    onExportExcel ? {
      key: '1',
      label: (
        <div className='flex gap-2 cursor-pointer' onClick={onExportExcel}>
          <RiFileExcel2Line/>
          Export Excel
        </div>
      ),
    } : null,
    onExportPDF ? {
      key: '2',
      label: (
        <div className='flex gap-2 cursor-pointer' onClick={onExportPDF}>
          <RiFilePdf2Line/>
          Export PDF
        </div>
      ),
    } : null,
  ];

  return (
    <Dropdown {...props} menu={{ items }}>
      <button
        className={cn(
          "cursor-pointer disabled:cursor-not-allowed",
          hoverDropdownVariants({ size }),
        )}
      >
        <RiContractRightLine
          className={hoverDropdownIconVariants({ size })}
        />
      </button>
    </Dropdown>
  );

};
