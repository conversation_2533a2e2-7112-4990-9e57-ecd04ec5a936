import { memo } from 'react';
import { cn } from '@/lib/utils';
import { cva } from 'class-variance-authority';

type ReportButtonProps = {
  type: "Apply" | "Reset";
  size?: "small" | "default" | "large";
  disabled?: boolean;
  onClick?: () => void;
};

const reportButtonVariants = cva(
  'border border-[#43298F] transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed',
  {
    variants: {
      type: {
        Apply: 'text-white bg-[#43298F]',
        Reset: 'text-[#43298F] bg-white',
      },
      size: {
        small: 'px-2 py-1 text-xs rounded-sm border-2 border-[#43298F]',
        default: 'px-6 py-2 text-sm rounded-sm',
        large: 'px-6 py-2 text-md rounded-md',
      },
    },
    defaultVariants: {
      size: 'default',
    },
  },
);

const ReportButton: React.FC<ReportButtonProps> = ({
  type,
  size = "default",
  disabled = false,
  onClick = () => {},
}) => {

  return (
    <button
      disabled={disabled}
      onClick={onClick}
      className={cn(
        "cursor-pointer disabled:cursor-not-allowed",
        reportButtonVariants({ type, size }),
      )}
    >
      {type}
    </button>
  );

};

export default memo(ReportButton);
