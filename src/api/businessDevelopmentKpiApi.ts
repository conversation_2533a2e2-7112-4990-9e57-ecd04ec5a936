import axios from 'axios';

const URL = import.meta.env.VITE_API_URL;

export interface BusinessDevelopmentKpiPayload {
  year: string | string[];
  month: string | string[];
  department?: string | string[] | null;
  businessType?: string | string[] | null;
  marketleader?: string | string[] | null;
  adminBu?: string | string[] | null;
}

export interface BusinessDevelopmentKpiResponse {
  property: string;
  type: string;
  client: string;
  unit: number;
  status: string;
  mgr: string;
  pm_fees_percent: number;
  pm_fees: number;
  annual_pm_fees: number;
}

export interface BusinessDevelopmentKpiApiResponse {
  data: BusinessDevelopmentKpiResponse[];
  sql?: string;
}

const createDefaultPayload = (): BusinessDevelopmentKpiPayload => ({
  year: '2025',
  month: ['1'],
  department: null,
  businessType: null,
  marketleader: null,
  adminBu: null,
});

export const getBusinessDevelopmentKpi = async (
  body?: BusinessDevelopmentKpiPayload,
) => {
  try {
    const payload = body || createDefaultPayload();
    const response = await axios.post<BusinessDevelopmentKpiApiResponse>(
      `${URL}/business-development-kpi`,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching business development KPI data:', error);
    return {
      data: [],
      sql: '',
    };
  }
};