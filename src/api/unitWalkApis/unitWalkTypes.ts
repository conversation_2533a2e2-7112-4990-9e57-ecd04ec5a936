export interface UnitWalkKPIPayload {
  year: string | string[];
  month: string | string[];
  department: string | string[] | null;
  businessType: string | string[] | null;
  marketleader: string | string[] | null;
  adminBu: string | string[] | null;
}

export interface UnitWalkKPIRegionTypes {
  Region: string;
  beginning: number | null;
  additions: number | null;
  losses: number | null;
  current: number | null;
}

export interface UnitWalkKPIYTDMarketTypes {
  RegionMarket: string;
  beginning: number | null;
  additions: number | null;
  losses: number | null;
  current: number | null;
}
