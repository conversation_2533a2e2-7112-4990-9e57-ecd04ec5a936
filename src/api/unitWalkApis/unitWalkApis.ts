import { DEFAULT_REPORTS_API_PAYLOAD } from '@/constants/constants';
import axios from 'axios';
import {
  UnitWalkKPIPayload,
  UnitWalkKPIRegionTypes,
  UnitWalkKPIYTDMarketTypes,
} from './unitWalkTypes';

const URL = import.meta.env.VITE_API_URL;

const defaultPayload = DEFAULT_REPORTS_API_PAYLOAD;

export const unitWalkKPIRegionApi = async (
  payload: UnitWalkKPIPayload,
): Promise<{ data: UnitWalkKPIRegionTypes[] }> => {
  if (!payload) {
    payload = defaultPayload;
  }
  const responce = await axios.post(`${URL}/unit-walk-kpi-region`, payload);
  const { data } = responce.data;
  return { data };
};

export const unitWalkKPIYTDMarketApi = async (
  payload: UnitWalkKPIPayload,
): Promise<{ data: UnitWalkKPIYTDMarketTypes[] }> => {
  if (!payload) {
    payload = defaultPayload;
  }
  const responce = await axios.post(`${URL}/unit-walk-kpi-market`, payload);
  const { data } = responce.data;
  return { data };
};
