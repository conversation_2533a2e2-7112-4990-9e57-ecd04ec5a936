import axios from 'axios';
import { LeaseLineItemRequest, LeaseLineItemsResponse } from '@/types/leaseLineItemsTypes';

const URL = import.meta.env.VITE_API_URL;

export const getLeaseLineItems = async (body: LeaseLineItemRequest): Promise<LeaseLineItemsResponse> => {
  try {
    const response = await axios.post<LeaseLineItemsResponse>(
      `${URL}/lease-line-items`,
      body,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching lease line items:', error);
    throw error;
  }
};