import axios from 'axios';

const URL = import.meta.env.VITE_API_URL;

export interface PropertyManagementKpiPayload {
  year: string | string[];
  month: string | string[];
  department?: string | string[] | null;
  businessType?: string | string[] | null;
  marketleader?: string | string[] | null;
  adminBu?: string | string[] | null;
}

export interface PropertyManagementKpiResponse {
  parameter: string;
  Central: number;
  East: number;
  West: number;
  Consolidated: number;
}

export interface PropertyManagementKpiApiResponse {
  data: PropertyManagementKpiResponse[];
  sql?: string;
}

const createDefaultPayload = (): PropertyManagementKpiPayload => ({
  year: '2025',
  month: ['1'],
  department: null,
  businessType: null,
  marketleader: null,
  adminBu: null,
});

export const getPropertyManagementYoY = async (
  body?: PropertyManagementKpiPayload,
) => {
  try {
    const payload = body || createDefaultPayload();
    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-yoy`,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching property management YoY data:', error);
    return {
      data: [],
      sql: '',
    };
  }
};

export const getPropertyManagementNOI = async (
  body?: PropertyManagementKpiPayload,
) => {
  try {
    const payload = body || createDefaultPayload();
    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-noi`,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching property management NOI data:', error);
    return {
      data: [],
      sql: '',
    };
  }
};

export const getPropertyManagementJTurner = async (
  body?: PropertyManagementKpiPayload,
) => {
  try {
    const payload = body || createDefaultPayload();
    const response = await axios.post<PropertyManagementKpiApiResponse>(
      `${URL}/property-management-jturner`,
      payload,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching property management J Turner data:', error);
    return {
      data: [],
      sql: '',
    };
  }
};
