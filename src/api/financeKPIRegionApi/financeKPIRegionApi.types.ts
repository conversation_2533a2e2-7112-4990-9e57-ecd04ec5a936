export interface FinanceKPIPayload {
  year: string | string[];
  month: string | string[];
  department: string | string[] | null;
  businessType: string | string[] | null;
  marketleader: string | string[] | null;
  adminBu: string | string[] | null;
}

export interface FinanceKPIRegionWiseResponce {
  Region: string;
  FeesType: string;
  Actual: number | null;
  budget: number | null;
  Variance: number | null;
  Variance_Pert: number | null;
  forecast: number | null;
  budget1: number | null;
  Variance1: number | null;
  FY_Variance_Pert: number | null;
}

export interface FinanceKPIRegionForecastResponse {
  regionmarket: string;

  // PM Fees
  'PM Fees_Forecast': number | null;
  'PM Fees_Budget': number | null;
  'PM Fees_Variance': number | null;
  'PM Fees_FY_Variance_Pert': number | null;

  // ROR
  ROR_Forecast: number | null;
  ROR_Budget: number | null;
  ROR_Variance: number | null;
  ROR_FY_Variance_Pert: number | null;

  // Fees/Unit
  'Fees/Unit_Forecast': number | null;
  'Fees/Unit_Budget': number | null;
  'Fees/Unit_Variance': number | null;
  'Fees/Unit_FY_Variance_Pert': number | null;

  // Cost/Unit
  'Cost/Unit_Forecast': number | null;
  'Cost/Unit_Budget': number | null;
  'Cost/Unit_Variance': number | null;
  'Cost/Unit_FY_Variance_Pert': number | null;

  // WAU
  WAU_Forecast: number | null;
  WAU_Budget: number | null;
  WAU_Variance: number | null;
  WAU_FY_Variance_Pert: number | null;

  // Actual Units
  'Actual Units_Forecast': number | null;
  'Actual Units_Budget': number | null;
  'Actual Units_Variance': number | null;
  'Actual Units_FY_Variance_Pert': number | null;
}

export interface FinanceKPIRegionActualResponse {
  regionmarket: string;

  'PM Fees_Actual': number | null;
  'PM Fees_Budget': number | null;
  'PM Fees_Varriance': number | null;
  'PM Fees_Variance_Pert': number | null;

  ROR_Actual: number | null;
  ROR_Budget: number | null;
  ROR_Varriance: number | null;
  ROR_Variance_Pert: number | null;

  'Fees/Unit_Actual': number | null;
  'Fees/Unit_Budget': number | null;
  'Fees/Unit_Varriance': number | null;
  'Fees/Unit_Variance_Pert': number | null;

  'Cost/Unit_Actual': number | null;
  'Cost/Unit_Budget': number | null;
  'Cost/Unit_Varriance': number | null;
  'Cost/Unit_Variance_Pert': number | null;

  WAU_Actual: number | null;
  WAU_Budget: number | null;
  WAU_Varriance: number | null;
  WAU_Variance_Pert: number | null;

  'Actual Units_Actual': number | null;
  'Actual Units_Budget': number | null;
  'Actual Units_Varriance': number | null;
  'Actual Units_Variance_Pert': number | null;
}

export interface FinacialMarketTableDataFormatedTypes {
  regionmarket: string;
  actual: number | null | undefined;
  budget: number | null | undefined;
  forecast: number | null | undefined;
  budget_forecast: number | null | undefined;
  variance: number | null | undefined;
  variance_forecast: number | null | undefined;
  unit: '' | '%' | '';
}
