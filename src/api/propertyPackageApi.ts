import axios from 'axios';

const URL = import.meta.env.VITE_API_URL;

interface PropertyPackageFilterOptionsResponse {
  years: number[];
  months: number[];
  departments: string[];
  businessTypes: string[];
  marketleaders: string[];
  adminBu: string[];
}

export const getPropertyPackageFilterOptions = async () => {
  try {
    const response = await axios.post<PropertyPackageFilterOptionsResponse>(
      `${URL}/property-package/filter-options`,
    );
    return response.data;
  } catch (error) {
    console.error('Error fetching property package filter options:', error);
    return {
      years: [],
      months: [],
      departments: [],
      businessTypes: [],
      marketleaders: [],
      adminBu: [],
    };
  }
};
