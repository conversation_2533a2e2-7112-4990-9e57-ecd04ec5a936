import { Link } from 'react-router-dom';
import { toAbsoluteUrl } from '@/lib/helpers';

export function SidebarFooter() {
  return (
    <div className="sidebar-footer flex items-center justify-start px-3 lg:px-6 py-4 shrink-0 border-t border-border">
      <Link to="/">
        <div className="dark:hidden">
          <img
            src={toAbsoluteUrl('/media/logo/logo1-light.png')}
            className="default-logo h-[52px] max-w-none"
            alt="Default Logo"
          />
          <img
            src={toAbsoluteUrl('/media/logo/logo-circle.png')}
            className="small-logo h-[32px] max-w-none"
            alt="Mini Logo"
          />
        </div>
        <div className="hidden dark:block">
          <img
            src={toAbsoluteUrl('/media/logo/logo1-dark.png')}
            className="default-logo h-[52px] max-w-none"
            alt="Default Dark Logo"
          />
          <img
            src={toAbsoluteUrl('/media/logo/logo-circle.png')}
            className="small-logo h-[32px] max-w-none"
            alt="Mini Logo"
          />
        </div>
      </Link>
    </div>
  );
}
