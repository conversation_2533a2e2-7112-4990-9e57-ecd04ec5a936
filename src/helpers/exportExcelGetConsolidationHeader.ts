export const getConsolidationHeader = (filters?: {
  month: string[];
  year: string;
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}): string => {
  if (!filters) return 'Master Consolidation';

  // Priority order: Admin BU > Market Leader > Department > Business Type
  if (filters.adminBU && filters.adminBU.length > 0) {
    const values = filters.adminBU.join(', ');
    return `Filtered by Admin BU: ${values}`;
  }

  if (filters.marketLeader && filters.marketLeader.length > 0) {
    const values = filters.marketLeader.join(', ');
    return `Filtered by Market Leader: ${values}`;
  }

  if (filters.department && filters.department.length > 0) {
    const values = filters.department.join(', ');
    // return `Filtered by Department: ${values}`;
    return `Filtered by Region: ${values}`;
  }

  if (filters.businessType && filters.businessType.length > 0) {
    const values = filters.businessType.join(', ');
    return `Filtered by Business Type: ${values}`;
  }

  return 'Master Consolidation';
};
