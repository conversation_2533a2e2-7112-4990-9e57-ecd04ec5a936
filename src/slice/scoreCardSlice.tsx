import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { Dayjs } from 'dayjs';
import { ScoreCardData } from '@/types/scorecardTypes';

export interface OccupancyMetric {
  label: string;
  value: string;
  isPositive?: boolean;
  color?: string;
}

export interface RentalMetric {
  label: string;
  value: string;
  subValue?: string;
  change?: string;
  isPositive?: boolean;
  color?: string;
}

export interface FinancialMetric {
  label: string;
  value: string;
  isPositive?: boolean;
  color?: string;
}

export interface PerformanceRow {
  category: string;
  period: string;
  target: string;
  actual: string;
  score: 'green' | 'yellow' | 'red';
}

export interface PropertyInfo {
  propertyCode: string;
  propertyName: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  submarket: string;
  units: number;
  begOfOps: string;
  assetType: string;
  yearBuilt: number;
  residentialSqft: number;
  avgResUnitSize: number;
  retailSqft: string;
  retailSpaces: number;
  assetClass: string;
  region: string;
  svp: string;
  rvp: string;
  vp: string;
  rpm: string;
  renewableUnits: number;
  nonRevenueUnits: number;
  downUnits: number;
  propertyStrategy: string;
  sameStore: string;
  imageUrl?: string;
}

export interface ReputationMetric {
  platform: string;
  rating: number;
  maxRating: number;
}

export interface SummaryText {
  title: string;
  content: string;
}

export interface ScorecardFilters {
  system: string;
  property: string;
  startDate: string;
  endDate: string;
  reportingPeriod: string;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface DateFilterOption extends FilterOption {
  dayjsValue?: Dayjs; // dayjs object for DatePicker
  monthKey?: string; // YYYY-MM format for easy comparison
}

export interface LoadingStates {
  propertyInfo: boolean;
  occupancyMetrics: boolean;
  rentalMetrics: boolean;
  financialMetrics: boolean;
  performanceRows: boolean;
  reputationMetrics: boolean;
  summaryTexts: boolean;
  filterOptions: boolean;
}

export interface ErrorStates {
  propertyInfo: string | null;
  occupancyMetrics: string | null;
  rentalMetrics: string | null;
  financialMetrics: string | null;
  performanceRows: string | null;
  reputationMetrics: string | null;
  summaryTexts: string | null;
  filterOptions: string | null;
}

export interface ScorecardState {
  title: string;
  filters: ScorecardFilters;
  availableProperties: FilterOption[];
  availableSystems: FilterOption[];
  availableDates: DateFilterOption[];
  rawSystemsData: Array<{
    System: string;
    BU: string;
    property_hmy: string;
    PropertyName: string;
  }>;
  minDate: Dayjs | null;
  maxDate: Dayjs | null;
  propertyInfo: PropertyInfo;
  occupancyMetrics: OccupancyMetric[];
  rentalMetrics: RentalMetric[];
  financialMetrics: FinancialMetric[];
  performanceRows: PerformanceRow[];
  reputationMetrics: ReputationMetric[];
  summaryTexts: SummaryText[];
  ytdTurnCost: string;
  financialDateRange: string;
  rawApiData: ScoreCardData | null;
  loading: boolean;
  loadingStates: LoadingStates;
  error: string | null;
  errorStates: ErrorStates;
}

const initialState: ScorecardState = {
  title: 'Willow Bridge Performance Scorecard',
  filters: {
    system: '',
    property: '',
    startDate: '',
    endDate: '',
    reportingPeriod: '',
  },
  availableProperties: [],
  availableSystems: [],
  availableDates: [],
  rawSystemsData: [],
  minDate: null,
  maxDate: null,
  propertyInfo: {
    propertyCode: '0',
    propertyName: '0',
    address: '0',
    city: '0',
    state: '0',
    zipCode: '0',
    submarket: '0',
    units: 0,
    begOfOps: '0',
    assetType: '0',
    yearBuilt: 0,
    residentialSqft: 0,
    avgResUnitSize: 0,
    retailSqft: '0',
    retailSpaces: 0,
    assetClass: '0',
    region: '0',
    svp: '0',
    rvp: '0',
    vp: '0',
    rpm: '0',
    renewableUnits: 0,
    nonRevenueUnits: 0,
    downUnits: 0,
    propertyStrategy: '0',
    sameStore: '0',
    imageUrl: undefined,
  },
  occupancyMetrics: [
    { label: 'OCCUPANCY NON REV', value: '0' },
    { label: 'SUBMARKET OCCUPANCY', value: '0' },
    { label: 'PERFORMANCE TO SUBMARKET', value: '0' },
    { label: 'OCCUPANCY TREND', value: '0' },
    { label: 'VARIANCE TO OCCUPANCY', value: '0' },
    { label: 'TREND GAIN/LOSS', value: '0' },
    { label: 'GAIN / LOSS', value: '0' },
    { label: 'OCCUPANCY TREND T30 VARIANCE', value: '0' },
    { label: 'UNITS AVAILABLE', value: '0' },
    { label: 'VACANT UNITS', value: '0' },
    { label: 'AGED VACANT UNITS', value: '0' },
    { label: 'AVG AGED DAYS VACANT', value: '0' },
    { label: 'SHOWS T30', value: '0' },
  ],
  rentalMetrics: [
    { label: 'NEW NET IN PLACE RENT', value: '0' },
    { label: 'NEW NET IN PLACE RENT YoY CHANGE', value: '0' },
    { label: 'RENEWAL NET IN PLACE RENT', value: '0' },
    { label: 'RENEWAL NET IN PLACE RENT YoY CHANGE', value: '0' },
    { label: 'NET IN PLACE RENT', value: '0' },
    { label: 'NET IN PLACE RENT YoY CHANGE', value: '0' },
    { label: 'MTM', value: '0' },
    { label: 'YTD RENEWAL CONVERSION', value: '0' },
  ],
  financialMetrics: [
    { label: 'RENTAL INCOME', value: '0' },
    { label: 'TOTAL INCOME', value: '0' },
    { label: 'CONTROLLABLE OP EX', value: '0' },
    { label: 'TOTAL OP EX', value: '0' },
    { label: 'NOI', value: '0' },
    { label: 'CONTROLLABLE NOI', value: '0' },
    // { label: 'CAPITAL', value: '0' },
  ],
  performanceRows: [
    {
      category: 'Collections % MTD',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: 'Bad Debt W/O (Net) as % of GRI (Market Rents) YTD',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: 'Collection Recovery Ratio (In House & Agency)',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: 'Average Unit Turn Time T90',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: 'Units With Repeat Service Tickets Within 30 Days',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: '% Tickets >72 Hours To Close T30',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
    {
      category: 'Capital Execution',
      period: '',
      target: '0',
      actual: '0',
      score: 'green',
    },
  ],
  reputationMetrics: [
    { platform: 'J Turner', rating: 0, maxRating: 100 },
    { platform: 'Google', rating: 0, maxRating: 5 },
  ],
  summaryTexts: [
    {
      title: 'Summary',
      content: '0',
    },
  ],
  ytdTurnCost: '0',
  financialDateRange: '',
  rawApiData: null,
  loading: false,
  loadingStates: {
    propertyInfo: false,
    occupancyMetrics: false,
    rentalMetrics: false,
    financialMetrics: false,
    performanceRows: false,
    reputationMetrics: false,
    summaryTexts: false,
    filterOptions: false,
  },
  error: null,
  errorStates: {
    propertyInfo: null,
    occupancyMetrics: null,
    rentalMetrics: null,
    financialMetrics: null,
    performanceRows: null,
    reputationMetrics: null,
    summaryTexts: null,
    filterOptions: null,
  },
};

export const scorecardSlice = createSlice({
  name: 'scorecard',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setSectionLoading: (
      state,
      action: PayloadAction<{ section: keyof LoadingStates; loading: boolean }>,
    ) => {
      state.loadingStates[action.payload.section] = action.payload.loading;
    },
    setSectionError: (
      state,
      action: PayloadAction<{
        section: keyof ErrorStates;
        error: string | null;
      }>,
    ) => {
      state.errorStates[action.payload.section] = action.payload.error;
    },
    setFilters: (state, action: PayloadAction<Partial<ScorecardFilters>>) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    setFilterOptions: (
      state,
      action: PayloadAction<{
        properties: FilterOption[];
        systems: FilterOption[];
        dates: DateFilterOption[];
        rawSystemsData?: Array<{
          System: string;
          BU: string;
          property_hmy: string;
          PropertyName: string;
        }>;
        minDate?: Dayjs | null;
        maxDate?: Dayjs | null;
      }>,
    ) => {
      state.availableProperties = action.payload.properties;
      state.availableSystems = action.payload.systems;
      state.availableDates = action.payload.dates;
      if (action.payload.rawSystemsData !== undefined)
        state.rawSystemsData = action.payload.rawSystemsData;
      if (action.payload.minDate !== undefined)
        state.minDate = action.payload.minDate;
      if (action.payload.maxDate !== undefined)
        state.maxDate = action.payload.maxDate;
    },
    setPropertyInfo: (state, action: PayloadAction<PropertyInfo>) => {
      state.propertyInfo = action.payload;
    },
    setOccupancyMetrics: (state, action: PayloadAction<OccupancyMetric[]>) => {
      state.occupancyMetrics = action.payload;
    },
    setRentalMetrics: (state, action: PayloadAction<RentalMetric[]>) => {
      state.rentalMetrics = action.payload;
    },
    setFinancialMetrics: (state, action: PayloadAction<FinancialMetric[]>) => {
      state.financialMetrics = action.payload;
    },
    setPerformanceRows: (state, action: PayloadAction<PerformanceRow[]>) => {
      state.performanceRows = action.payload;
    },
    setReputationMetrics: (
      state,
      action: PayloadAction<ReputationMetric[]>,
    ) => {
      state.reputationMetrics = action.payload;
    },
    setSummaryTexts: (state, action: PayloadAction<SummaryText[]>) => {
      state.summaryTexts = action.payload;
    },
    setYtdTurnCost: (state, action: PayloadAction<string>) => {
      state.ytdTurnCost = action.payload;
    },
    setFinancialDateRange: (state, action: PayloadAction<string>) => {
      state.financialDateRange = action.payload;
    },
    setRawApiData: (state, action: PayloadAction<ScoreCardData | null>) => {
      state.rawApiData = action.payload;
    },
    setAllScorecardData: (
      state,
      action: PayloadAction<Partial<ScorecardState>>,
    ) => {
      return { ...state, ...action.payload };
    },
    setSystemFilterValue: (state, action: PayloadAction<string>) => {
      state.filters.system = action.payload;
    },
  },
});

export const {
  setLoading,
  setError,
  setSectionLoading,
  setSectionError,
  setFilters,
  setFilterOptions,
  setPropertyInfo,
  setOccupancyMetrics,
  setRentalMetrics,
  setFinancialMetrics,
  setPerformanceRows,
  setReputationMetrics,
  setSummaryTexts,
  setYtdTurnCost,
  setFinancialDateRange,
  setRawApiData,
  setAllScorecardData,
  setSystemFilterValue,
} = scorecardSlice.actions;

export default scorecardSlice.reducer;
