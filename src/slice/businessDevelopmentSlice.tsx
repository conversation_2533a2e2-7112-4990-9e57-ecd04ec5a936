import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface BusinessDevelopmentItem {
  id: string;
  property: string;
  type: 'Win' | 'Loss';
  client: string;
  units: number;
  status: string;
  manager: string;
  pmFeePercent: number;
  pmFeePer25: number;
  annualPmFees: number;
}

export interface BusinessDevelopmentFilters {
  year: string;
  month: string[];
  department: string[];
  businessType: string[];
  marketLeader: string[];
  adminBU: string[];
}

export interface BusinessDevelopmentState {
  items: BusinessDevelopmentItem[];
  filters: BusinessDevelopmentFilters;
  loading: boolean;
  error: string | null;
  lastUpdated: string;
}

const initialState: BusinessDevelopmentState = {
  items: [],
  filters: {
    year: '2025',
    month: ['January'],
    department: [],
    businessType: [],
    marketLeader: [],
    adminBU: [],
  },
  loading: false,
  error: null,
  lastUpdated: 'January 2025',
};

export const businessDevelopmentSlice = createSlice({
  name: 'businessDevelopment',
  initialState,
  reducers: {
    setLoading: (state, action: PayloadAction<boolean>) => {
      state.loading = action.payload;
    },
    setError: (state, action: PayloadAction<string | null>) => {
      state.error = action.payload;
    },
    setBusinessDevelopmentData: (
      state,
      action: PayloadAction<{
        items: BusinessDevelopmentItem[];
        lastUpdated: string;
      }>,
    ) => {
      state.items = action.payload.items;
      state.lastUpdated = action.payload.lastUpdated;
    },
    setLastUpdated: (state, action: PayloadAction<string>) => {
      state.lastUpdated = action.payload;
    },
    setFilters: (
      state,
      action: PayloadAction<Partial<BusinessDevelopmentFilters>>,
    ) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    resetFilters: (state) => {
      state.filters = initialState.filters;
    },
  },
});

export const {
  setLoading,
  setError,
  setBusinessDevelopmentData,
  setLastUpdated,
  setFilters,
  resetFilters,
} = businessDevelopmentSlice.actions;

export default businessDevelopmentSlice;