trigger:     
  branches:
    include:
      - 'dev'

pool:
  vmImage: 'ubuntu-latest'

variables:
  resourceGroupName: 'rg-datamovement-cus-dev'
  storageAccountName: 'willowbridgereactdev'
  buildDirectoryPath: '$(System.DefaultWorkingDirectory)/dist'


stages:
- stage: Build_and_Deploy
  jobs:
  - job: Build
    steps:
    - script: |
        echo "Setting environment variables..." 
        export VITE_API_URL=$(VITE_API_URL) 
        export VITE_APP_APP_CLIENT_ID=$(VITE_APP_APP_CLIENT_ID) 
        export VITE_APP_AZURE_TENANT_ID=$(VITE_APP_AZURE_TENANT_ID) 
        export VITE_APP_REDIRECT_URI=$(VITE_APP_REDIRECT_URI) 
        export VITE_APP_FUNC_EXPOSED_API=$(VITE_APP_FUNC_EXPOSED_API)
        export VITE_APP_VERSION=$(VITE_APP_VERSION)
        export VITE_APP_ENV=$(VITE_APP_ENV)
        npm ci
        npm run build
    - task: CopyFiles@2
      inputs:
        contents: 'dist/**/*'
        targetFolder: '$(Build.ArtifactStagingDirectory)'
      displayName: 'Copy files to artifact staging directory'
      
    - task: AzureCLI@2
      inputs:
        azureSubscription: "Prism-SC"
        scriptType: 'bash'
        scriptLocation: 'inlineScript'
        inlineScript: |
          az storage blob service-properties update --account-name $(storageAccountName) --static-website --404-document index.html --index-document index.html
          az storage blob upload-batch -s $(Build.ArtifactStagingDirectory)/dist -d '$web' --account-name $(storageAccountName) --overwrite
          az storage account show -n $(storageAccountName) -g $(resourceGroupName) --query "primaryEndpoints.web" --output tsv
      displayName: 'Upload files to Azure Blob Storage'


      
